﻿.container {
    border-radius: 80px 80px 0 0;
    height: 90%;
    background-color: var(--app-background);
    gap: 30px;
    padding: 40px;
    width: 100%;
    position: absolute;
    animation: smooth-appear 1s ease forwards;
}

@keyframes smooth-appear {
    from {
        bottom: -100%;
        opacity: 0;
    }

    to {
        bottom: 0;
        opacity: 1;
    }
}

.content {
    background-color: #FFFFFF;
    border-radius: 80px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.body {
    width: 470px;
    height: 100%;
    display: flex;
    position: relative;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    gap: 0px;
}

.img-container {
    margin-top: 30px;
    position: relative;
}

.img {
    width: 360px;
    height: 360px;
}

.notice-container {
    position: absolute;
    display: flex;
    justify-content: end;
    gap: 10px;
    width: max-content;
    max-width: 38vw;
}

.notice-container p {
    font-family: Roboto, serif;
    font-size: 24px;
    font-weight: 600;
    line-height: 32px;
}

.left-notice {
    text-align: right;
}

.notice-1 {
    top: 75px;
    right: 270px;
}

.notice-2 {
    top: 155px;
    right: 270px;
}

.notice-3 {
    top: 235px;
    right: 270px;
}

.notice-4 {
    flex-direction: row-reverse;
    justify-content: start;
    top: 75px;
    left: 270px;
}

.notice-5 {
    flex-direction: row-reverse;
    justify-content: start;
    top: 155px;
    left: 270px;
}

.notice-6 {
    flex-direction: row-reverse;
    justify-content: start;
    top: 235px;
    left: 270px;
}

.conformity {
    font-family: Futura PT, serif;
    font-size: 28px;
    font-weight: 700;
    line-height: 34px;
    text-align: center;
}

.close-btn {
    cursor: pointer;
    display: flex;
    gap: 14px;
    padding: 40px 50px 40px 50px;
    border-radius: 70px;
    border: none;
    background: linear-gradient(180deg, #ED7225 0%, #D64518 100%);
    font-size: 28px;
    font-weight: 700;
    line-height: 30px;
    letter-spacing: -1px;
    text-align: left;
    color: white;
    margin-bottom: 20px;
}

.onboarding-step-image {
    margin-bottom: 40px;
}

.close-btn img {
    width: 30px;
    height: 30px;
}

@media (min-width: 1365px) and (max-width: 1367px) and (min-height: 767px) and (max-height: 769px) {
    .img {
        width: 240px;
        height: 240px;
    }

    .notice-1 {
        top: 40px;
        right: 170px;
    }

    .notice-2 {
        top: 90px;
        right: 170px;
    }

    .notice-3 {
        top: 140px;
        right: 170px;
    }

    .notice-4 {
        flex-direction: row-reverse;
        justify-content: start;
        top: 40px;
        left: 170px;
    }

    .notice-5 {
        flex-direction: row-reverse;
        justify-content: start;
        top: 90px;
        left: 170px;
    }

    .notice-6 {
        flex-direction: row-reverse;
        justify-content: start;
        top: 140px;
        left: 170px;
    }
}

@media (min-width: 1919px) and (max-width: 1921px) and (min-height: 1079px) and (max-height: 1081px) {
    .body {
        width: 700px;
    }

    .conformity {
        font-size: 35px;
        line-height: 40px;
    }
}
