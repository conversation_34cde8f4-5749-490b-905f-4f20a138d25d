﻿@using Kis.Framework.Ui.Components
@using Kis.SoundManager
@using Kis.Framework.Ui.LocalizationTool
@using Kis.Framework.Basic.ProjectInformation.Configuration.Operator
@inherits LayoutComponentBase
@inject ILocalization i18n
@inject OperatorConfiguration OperatorConfiguration
@inject NavigationManager Navigation
@inject AppState AppState

<SoundPlayer PlaylistFilename="SoundPlaylists.json" CurrentLanguage="@i18n.CurrentLanguage.Name" @ref="Player" />

<div id="application" @onclick="ResetTimeout">
    <CascadingValue Value="Player">

	@if (_showTimeoutWarning)
	{
	<Popup Title="@i18n.TranslateText("global.stillthere.title")"
	       OkText="@i18n.TranslateText("global.stillthere.continuer")"
	       CancelText="@i18n.TranslateText("global.stillthere.cancel")"
	       OkButtonColor="@Popup.GreenButton"
	       CancelButtonColor="@Popup.OrangeButton"
	       CancelButtonTextColor="@Popup.WhiteText"
	       OnOk="ResetTimeout"
           OnCancel="@(() => NavigationManager.NavigateTo(State.TimeoutUrl))"
           SoundToPlay="Timeout"/>
	}
	
	<div class="container">
		<header class="container-header">
				<button id="button_quit" class="button-quit" @onclick="ShowCloseModal">
					<img id="image_exit" alt="@i18n.TranslateText("Global.Exit")" class="button-quit-icon" src="images/quit.svg" />
				@i18n.TranslateText("Global.Exit")
			</button>
			<div class="breadcrumb">
				<div class="breadcrumb-step-container">
					<div class="breadcrumb-step @(Navigation.Uri.Contains("live") ? "active" : "")">
						1
					</div>
						<div id="text_live" class="breadcrumb-step-label @(Navigation.Uri.Contains("live") ? "active" : "")">
						@i18n.TranslateText("Live.Capture")
					</div>
				</div>

					<img id="image_chevron_right" alt="" class="breadcrumb-icon"
					 src="images/chevron-right.svg" />

				<div class="breadcrumb-step-container">
					<div class="breadcrumb-step @(Navigation.Uri.Contains("preview") ? "active" : "")">
						2
					</div>
						<div id="text_preview" class="breadcrumb-step-label @(Navigation.Uri.Contains("preview") ? "active" : "")">
						@i18n.TranslateText("Live.Validation")
					</div>
				</div>
			</div>
				<button id="button_instructions" class="instructions-btn" @onclick="ShowIntructions">
				@i18n.TranslateText("Live.Instructions")
					<img id="image_chevron_right" alt="list-check" class="instructions-btn-icon" src="images/list-check.svg" />
			</button>
		</header>
		
		@Body
		
		<DefaultFooter />
	</div>

	@if (_showInstructions || _showCloseModal)
	{
		<div class="overlay">
		</div>
	}

	@if (_showInstructions)
	{
		<div class="instructions-container">
			<header class="instructions-container-header">
					<img id="image_hide_instructions" alt="close" class="instructions-container-close-btn"
					 @onclick="HideInstructions"
					 src="images/close_white.svg" />
			</header>
			<CascadingValue Value="Player">
				<Instructions ShowCloseButton="true" OnClose="HideInstructions" />
			</CascadingValue>
		</div>
	}
	@if (_showCloseModal)
	{
		<Popup Title="@i18n.TranslateText("Global.ConfirmExit")"
			   Message="@i18n.TranslateText("Global.ExitConsequences")"
			   OkText="@i18n.TranslateText("Global.Exit")"
			   CancelText="@i18n.TranslateText("Global.Cancel")"
			   OkButtonColor="@Popup.OrangeButton"
			   CancelButtonColor="@Popup.TransparentButton"
			   CancelButtonTextColor="@Popup.BlackText"
			   OnOk="ExitProduct"
               OnCancel="HideCloseModal"
               SoundToPlay="BackHome" />
	}
    </CascadingValue>
</div>
