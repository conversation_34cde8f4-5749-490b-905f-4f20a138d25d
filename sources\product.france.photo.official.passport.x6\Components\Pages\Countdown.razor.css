﻿.container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: var(--app-background);
    padding: 75px 60px 50px 60px;
    transition: none;
    position: fixed;
    gap: 30px;
}

.countdown {
    position: absolute;
    top: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    z-index: 0;
    padding: 75px 60px 40px 60px;
    background-color: var(--app-background);
    gap: 30px;
    margin-left: -60px; /* to center the countdown because of Blazor Layout */
}

.black-screen {
    width: 100%;
    height: 100%;
    background-color: black;
    position : absolute;
}

.waiting-spinner{
    width: 25%;
    align-self: center;
}

.title {
    font-size: 30px;
    line-height: 40px;
    letter-spacing: -1px;
    text-align: left;
}

.body {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-evenly;
    padding: 50px;
    height: 100%;
    border-radius: 30px;
    border: 4px solid white;
}

.count {
    font-size: 200px;
    font-weight: 700;
    line-height: 200px;
    letter-spacing: -1px;
    text-align: center;
}
.hidden {
    visibility: hidden;
}

.notice {
    font-size: 58px;
    font-weight: 700;
    line-height: 64px;
    letter-spacing: -2px;
    text-align: center;
}

.body-title {
    width: 70%;
    font-family: Futura PT, serif;
    font-size: 58px;
    font-weight: 700;
    line-height: 64px;
    letter-spacing: -2px;
    text-align: center;
}