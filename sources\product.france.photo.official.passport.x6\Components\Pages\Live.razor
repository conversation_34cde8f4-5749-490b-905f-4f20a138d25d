@page "/live"
@using Kis.Framework.Ui.LocalizationTool
@inject ILocalization i18n
@inject AppState State

<div class="container">
    <div class="container-left">
        <h1 id="text_id_card" class="container-left-title">
            @i18n.TranslateText("Live.IdCard")
        </h1>
        <div style="position:relative">
            <span id="text_eyes_zone" class="live-eyes-zone-help">
                @i18n.TranslateText("Live.EyesZone")
            </span>
            <img id="image_illu_help" alt="" class="container-lef-help" src="images/illu-help.png" />
        </div>
    </div>

    <div class="container-center">
        @if (!string.IsNullOrEmpty(_imageBase64))
        {
            <img id="image_portrait" alt="portrait" class="live-image" src="@_imageBase64" />
        }
        <img id="image_marks" alt="marks" class="live-image-marks" src="images/ellipse.png" />
        <div class="capture-button-container" @onclick="TakePicture">
            <img id="image_take_picture" alt="marks" class="capture-button"
                 src="images/capture-button.svg"/>
            <img id="camera-icon" alt="camera-icon" src="images/camera-icon.svg" 
                 class="camera-icon" />
        </div>
    </div>

    <div class="container-right">
        <div class="tries-container">
            <p id="text_tries" class="tries-container-title">
                @i18n.TranslateText("Live.Tries")
            </p>
            <div class="tries-container-body">
                <p id="text_tries_from" class="tries-from">
                    0@(State.CurrentPicture + 1)
                </p>
                <p id="text_total_tries" class="tries-to">
                    @i18n.TranslateText("Live.TotalTries")
                </p>
            </div>
        </div>

        <div class="controls-container">
            @if (State.MachineType == MachineType.FIXED_STOOL || State.MachineType == MachineType.FIXED_CAMERA)
            {
                <p id="text_height_set" class="controls-container-title">
                    @i18n.TranslateText("Live.HeightSet")
                </p>
            }
            

            @if (State.MachineType == MachineType.FIXED_STOOL)
            {
                <div class="controls-button">
                    <img id="button_up_arrow" alt="button-up" class="container-live-body-right-high-button" src="images/up-arrow.svg"
                        @ontouchstart="() => MoveCamera(true)" @onmousedown="() => MoveCamera(true)" @ontouchend="() => StopCamera()" @onmouseup="() => StopCamera()" @onpointerout="() => StopCamera()" />
                </div>
                <div class="controls-button">
                    <img id="button_down_arrow" alt="button-down" class="container-live-body-right-high-button" src="images/down-arrow.svg"
                        @ontouchstart="() => MoveCamera(false)" @onmousedown="() => MoveCamera(false)" @ontouchend="() => StopCamera()" @onmouseup="() => StopCamera()" @onpointerout="() => StopCamera()" />
                </div>
            }
            else if (State.MachineType == MachineType.FIXED_CAMERA)
            {
                <img alt="stool" class="img-stool" src="images/stool.png" />
            }
            
        </div>
    </div>
</div>
