@using Kis.Framework.Ui.LocalizationTool
@inject ILocalization i18n
@inject NavigationManager NavigationManager

<nav id="drawer-lang">
    <div class="top" onclick="onCloseLangDrawer()">
        <p id="text_lang_picker_title" class="title">
            @i18n.TranslateText("LangPicker.Text.Header")
        </p>
        <div class="drawer-container-close">
            <img alt="close" class="close" src="images/icon-close.svg"/>
        </div>
    </div>
    <ul class="list-lang">
        @foreach (var lang in Languages)
        {
            <li id="text_set_lang" class="list-lang-item" @onclick="() => SetLanguage(lang)">
                <img id="image_lang_name" alt="flag-@(lang.Name)" class="list-lang-item-icon" src="@GetFlag(lang.Name)" />
                <p class="list-lang-item-label-used">
                    @lang.NativeName[0].ToString().ToUpper()@lang.NativeName[1..]
                    @if (lang.Name == i18n.CurrentLanguage.Name)
                    {
                        <span id="text_lang_used" class="list-lang-item-used">
                            @i18n.TranslateText("LangPicker.Text.Used")
                        </span>
                    }
                </p>
                <img id="image_current_lang" alt="radio" class="radio-lang"
                     src="@(lang.Name == i18n.CurrentLanguage.Name ? "images/radio-lang-checked.svg" : "images/radio-lang-unchecked.svg")"/>
            </li>
        }
    </ul>
</nav>