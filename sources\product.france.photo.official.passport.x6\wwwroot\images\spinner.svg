<svg width="76" height="79" viewBox="0 0 76 79" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_281_13698)">
<circle cx="38" cy="7" r="7" fill="url(#paint0_linear_281_13698)"/>
<circle cx="21" cy="12" r="6" fill="url(#paint1_linear_281_13698)"/>
<circle cx="8.5" cy="24.5" r="4.5" fill="url(#paint2_linear_281_13698)"/>
<circle cx="4" cy="41" r="4" fill="url(#paint3_linear_281_13698)"/>
<circle cx="9" cy="58" r="4" fill="url(#paint4_linear_281_13698)"/>
<circle cx="21" cy="70" r="4" fill="url(#paint5_linear_281_13698)"/>
<circle cx="38" cy="75" r="4" fill="url(#paint6_linear_281_13698)"/>
<circle cx="55" cy="70" r="4" fill="url(#paint7_linear_281_13698)"/>
<circle cx="55" cy="12" r="4" fill="url(#paint8_linear_281_13698)"/>
<circle cx="67" cy="24" r="4" fill="url(#paint9_linear_281_13698)"/>
<circle cx="67" cy="58" r="4" fill="url(#paint10_linear_281_13698)"/>
<circle cx="72" cy="41" r="4" fill="url(#paint11_linear_281_13698)"/>
</g>
<defs>
<linearGradient id="paint0_linear_281_13698" x1="38" y1="0" x2="38" y2="14" gradientUnits="userSpaceOnUse">
<stop stop-color="#ED7225"/>
<stop offset="1" stop-color="#D64518"/>
</linearGradient>
<linearGradient id="paint1_linear_281_13698" x1="21" y1="6" x2="21" y2="18" gradientUnits="userSpaceOnUse">
<stop stop-color="#ED7225"/>
<stop offset="1" stop-color="#D64518"/>
</linearGradient>
<linearGradient id="paint2_linear_281_13698" x1="8.5" y1="20" x2="8.5" y2="29" gradientUnits="userSpaceOnUse">
<stop stop-color="#ED7225"/>
<stop offset="1" stop-color="#D64518"/>
</linearGradient>
<linearGradient id="paint3_linear_281_13698" x1="4" y1="37" x2="4" y2="45" gradientUnits="userSpaceOnUse">
<stop stop-color="#ED7225"/>
<stop offset="1" stop-color="#D64518"/>
</linearGradient>
<linearGradient id="paint4_linear_281_13698" x1="9" y1="54" x2="9" y2="62" gradientUnits="userSpaceOnUse">
<stop stop-color="#ED7225"/>
<stop offset="1" stop-color="#D64518"/>
</linearGradient>
<linearGradient id="paint5_linear_281_13698" x1="21" y1="66" x2="21" y2="74" gradientUnits="userSpaceOnUse">
<stop stop-color="#ED7225"/>
<stop offset="1" stop-color="#D64518"/>
</linearGradient>
<linearGradient id="paint6_linear_281_13698" x1="38" y1="71" x2="38" y2="79" gradientUnits="userSpaceOnUse">
<stop stop-color="#ED7225"/>
<stop offset="1" stop-color="#D64518"/>
</linearGradient>
<linearGradient id="paint7_linear_281_13698" x1="55" y1="66" x2="55" y2="74" gradientUnits="userSpaceOnUse">
<stop stop-color="#ED7225"/>
<stop offset="1" stop-color="#D64518"/>
</linearGradient>
<linearGradient id="paint8_linear_281_13698" x1="55" y1="8" x2="55" y2="16" gradientUnits="userSpaceOnUse">
<stop stop-color="#ED7225"/>
<stop offset="1" stop-color="#D64518"/>
</linearGradient>
<linearGradient id="paint9_linear_281_13698" x1="67" y1="20" x2="67" y2="28" gradientUnits="userSpaceOnUse">
<stop stop-color="#ED7225"/>
<stop offset="1" stop-color="#D64518"/>
</linearGradient>
<linearGradient id="paint10_linear_281_13698" x1="67" y1="54" x2="67" y2="62" gradientUnits="userSpaceOnUse">
<stop stop-color="#ED7225"/>
<stop offset="1" stop-color="#D64518"/>
</linearGradient>
<linearGradient id="paint11_linear_281_13698" x1="72" y1="37" x2="72" y2="45" gradientUnits="userSpaceOnUse">
<stop stop-color="#ED7225"/>
<stop offset="1" stop-color="#D64518"/>
</linearGradient>
<clipPath id="clip0_281_13698">
<rect width="76" height="79" fill="white"/>
</clipPath>
</defs>
</svg>
