﻿using Kis.Framework.gRPC.Client;
using Kis.Share.Model.RemoteTransfer.Output;

namespace Kis.Product.France.Photo.Official.Passport.X6.Services;

public class AppState
{
	public const string ProductName = "product.france.photo.official.passport.x6";
	public int CurrentPicture { get; set; } = 0;
	public int ChosenPicture { get; set; } = 0;
	public string SuccessReturnUrl { get; set; } = "";
	public string ErrorReturnUrl { get; set; } = "";
	public string CancelReturnUrl { get; set; } = "";
	public string TimeoutUrl { get; set; } = "";
	public Guid GalleryGuid { get; set; }
	public Guid ProductId { get; set; }
	public List<byte[]> RawImages { get; set; } = [];
	public string[] Base64IcaoImages { get; set; } = new string[3];
	public Dictionary<int, IcaoAnalysis> AnalysisResults { get; set; } = new(3);
	public CancellationTokenSource LiveCancellationTokenSource { get; set; } = new();
	public MachineType MachineType { get; set; } = MachineType.FIXED_STOOL;
    public Dictionary<ProductOutput, int> ItemsInCart { get; } = [];

    public void Reset()
	{
		LiveCancellationTokenSource?.Cancel();
		CurrentPicture = 0;
		RawImages = new List<byte[]>();
		Base64IcaoImages = new string[3];
		AnalysisResults.Clear();
		ProductId = Guid.NewGuid();
        ItemsInCart.Clear();
        GC.Collect();
		GC.WaitForPendingFinalizers();
	}
}

public enum MachineType
{
    FIXED_STOOL,
    FIXED_CAMERA,
    FIXED_CAMERA_STOOL
}