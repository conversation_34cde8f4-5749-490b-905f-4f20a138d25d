﻿using Kis.Framework.gRPC.Client;
using Kis.Framework.gRPC.Client.Model;
using Kis.Library.Camera.Common;
using Kis.Library.Camera.Core;
using Kis.SoundManager;
using Microsoft.AspNetCore.Components;
using Microsoft.IO;
using System.Drawing.Imaging;
using Kis.Product.France.Photo.Official.Passport.X6.Services;
using EMotorSpeed = Kis.Framework.gRPC.Client.Model.EMotorSpeed;
using Kis.Framework.gRPC.Protos;
using Kis.Framework.Ui.Services;

namespace Kis.Product.France.Photo.Official.Passport.X6.Components.Pages;

public partial class Live
{
    [Inject] private NavigationService Navigation { get; set; } = default!;
	[Inject] private ILogger<Live> Logger { get; set; } = default!;
	[Inject] private IServiceIoBoardClient ServiceIoBoardClient { get; set; } = default!;
	[Inject] private ICameraBusinessCore Camera { get; set; } = default!;

	[CascadingParameter] public SoundPlayer Player { get; set; } = default!;

	private bool _isCameraMoving = false;
	private string _imageBase64 = "";
	private static readonly SemaphoreSlim s_Lock = new(1);
    private readonly SemaphoreSlim _liveStreamLock = new(1);

    private static readonly RecyclableMemoryStreamManager.Options s_options = new()
	{
		MaximumStreamCapacity = 1024 * 1024 * 10,
		MaximumBufferSize = 1024 * 1024 * 10
	};

	private static readonly RecyclableMemoryStreamManager s_manager = new(s_options);

	private Task? _liveTask;

	protected override async Task OnInitializedAsync()
	{
		try
		{
            if (!await ServiceIoBoardClient.SetLightMode(ELightMode.LiveGrayBackground.ToString()))
            {
                Navigation.NavigateToError("Cannot set LiveGrayBackground light mode", "ioboard");
                return;
            }

            var init = await Camera.Initialize();
            if (!init)
            {
                Navigation.NavigateToError("Unable to initialize the camera", "camera");
                return;
            }
            if (_liveTask is null)
            {
                State.LiveCancellationTokenSource = new();
                _liveTask = ExecuteLive(State.LiveCancellationTokenSource.Token);

            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"Error while initialize the camera");
            Navigation.NavigateToError("Unable initialize the camera", "camera");
        }
    }

	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		if (firstRender)
		{
            if (State.MachineType == MachineType.FIXED_CAMERA || State.MachineType == MachineType.FIXED_CAMERA_STOOL)
            {
                await Player.Play("LiveViewFixedCamera");
            }
            else
            {
                await Player.Play("LiveView");
            }
        }
	}

	private async Task ExecuteLive(CancellationToken token)
	{
        await _liveStreamLock.WaitAsync();
        ResolutionMode resolutionMode = ResolutionMode.LiveGrayBackground;

        try
		{
            var result = await Camera.PrepareCamera(resolutionMode);
            if (!result)
            {
                Navigation.NavigateToError("Unable to prepare the camera", "camera");
                return;
            }

            bool error = false;
            await foreach (var image in Camera.ExecuteCycleLive(resolutionMode, token))
            {
                if (image is null)
                {
                    Logger.LogError("image from Camera.ExecuteCycleLive() is null");
                    error = true;
                    break;
                }


                try
                {
                    if (token.IsCancellationRequested) break;

                    if (s_Lock.CurrentCount == 0) continue;

                    await s_Lock.WaitAsync(token);
                    await using var stream = s_manager.GetStream("live", 1024 * 1024 * 2);
                    image.Save(stream, ImageFormat.Jpeg);
                    _imageBase64 =
                        $"data:image/jpeg;base64,{Convert.ToBase64String(stream.GetReadOnlySequence().FirstSpan)}";
                    await InvokeAsync(StateHasChanged);
                }
                finally
                {
                    s_Lock.Release();
                    image.Dispose();
                }
            }

            // If the error is not due to the cancellation of the token (click on take photo), we navigate to the error page
            if (!token.IsCancellationRequested)
            {
                Navigation.NavigateToError("Error while live photo retrieval", "camera");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"Error while execute live the camera");
            Navigation.NavigateToError("Unable to execute live the camera", "camera");
        }
        finally
        {
            _liveStreamLock.Release();
        }
    }

	private async Task MoveCamera(bool up)
	{
        try
        {
            if (!_isCameraMoving)
            {
                _isCameraMoving = true;
                Framework.gRPC.Client.Model.EMotorDirection motorDirection = up ? Framework.gRPC.Client.Model.EMotorDirection.TowardsZeroSensor : Framework.gRPC.Client.Model.EMotorDirection.TowardsEndSensor;
                var result = await ServiceIoBoardClient.MoveMotorToDirection(EMotorId.Camera, EMotorSpeed.Medium, motorDirection);
                Logger.LogDebug($"MoveCamera(up = {up}) result = {result}");
                if (result != EMotorCommandResult.Ok && result != EMotorCommandResult.CommandStopped && result != EMotorCommandResult.SensorError)
                {
                    Navigation.NavigateToError(@"Error while MoveCamera(up = {up})", "ioboard");
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"Error while move camera");
            Navigation.NavigateToError($"Cannot move motor to direction: {ex}", "ioboard");
        }
    }

	private async Task StopCamera()
	{
		if (_isCameraMoving)
		{
			await ServiceIoBoardClient.MotorStop(EMotorId.Camera);
			_isCameraMoving = false;
			Logger.LogDebug("StopCamera()");
		}
	}

	private async Task TakePicture()
	{
        try
        {
            await State.LiveCancellationTokenSource.CancelAsync();
            // Wait for ExecuteLive() to finish
            // This call to WaitAsync doesn't need a Release call later because TakePicture only runs one time during the live screen's lifecycle
            await _liveStreamLock.WaitAsync();
        }
        catch (Exception ex)
        {
            Navigation.NavigateToError($"Error while canceling live: {ex}", "camera");
            return;
        }
		if (_liveTask != null)
			await _liveTask.WaitAsync(TimeSpan.FromSeconds(10));

		Navigation.NavigateTo("/countdown");
	}
}