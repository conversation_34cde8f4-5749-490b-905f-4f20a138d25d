﻿#drawer-lang {
    max-height: 100%;
    overflow-y: scroll;
    z-index: 3;
    position: fixed;
    width: 460px;
    left: -460px;
    height: 100%;
    top: 0;
    background: #FFFFFF;
    transition: left .5s;
}

.top {
    display: flex;
    justify-content: space-between;
    background: #EDEAE5;
    padding: 80px 30px 20px 30px
}

.title {
    font-family: Futura PT, serif;
    font-size: 30px;
    font-weight: 700;
}

.close {
    width: 30px;
    height: auto;
}

.list-lang {
    display: flex;
    flex-direction: column;
    padding: 30px;
    margin: 0;
    list-style-type: none;
}

.list-lang-item-icon {
    width: 30px;
    height: 30px;
    margin-top: auto;
    margin-bottom: auto;
}

.list-lang-item {
    cursor: pointer;
    display: flex;
    align-items: center;
    font-family: Roboto, serif;
    font-size: 24px;
    font-weight: 600;
    gap: 10px;
    border-top: 1px solid #E7E7E7;
    padding-top: 20px;
    padding-bottom: 20px;
}

.list-lang-item-label-used {
    display: flex;
    align-items: center;
    gap: inherit;
}

.list-lang-item-used {
    font-family: Roboto, serif;
    font-size: 16px;
    font-weight: 500;
    line-height: 28px;
    color: #ED7225;
}

.radio-lang {
    margin-left: auto;
}
