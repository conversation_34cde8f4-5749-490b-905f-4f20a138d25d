﻿:root {
    --app-background: #D6D4CF;
}

html, body {
    overscroll-behavior-x: none;
}

body {
    margin: 0;
    font-family: 'Futura PT', serif;
    touch-action: manipulation;
}

body * {
    cursor: var(--app-cursor) !important;
}

*, *:before, *:after {
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -ms-box-sizing: border-box;
    user-select: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    -webkit-tap-highlight-color: transparent;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
	user-drag: none;
    -webkit-user-drag: none;
}

*::selection {
    color: transparent;
    background: transparent;
}

p, h1, h2 {
    margin: 0;
}

#application {
    position: fixed;
    height: 100vh;
    width: 100%;
}

.spinner {
    animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
    to {
        transform: rotate(360deg);
    }
}
