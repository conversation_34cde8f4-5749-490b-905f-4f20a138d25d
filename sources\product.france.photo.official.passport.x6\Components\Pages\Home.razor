@page "/"
@using Kis.Framework.Ui.LocalizationTool
@using Kis.SoundManager
@inject IConfiguration Configuration
@inject ILocalization i18n

@layout EmptyLayout

<div class="container">
	<SoundPlayer PlaylistFilename="SoundPlaylists.json" CurrentLanguage="@i18n.CurrentLanguage.Name" @ref="Player" />
	<CascadingValue Value="Player">
		<Header ShowCartButton="@(Configuration.GetValue<bool>("AutoGoToCart", false))">
			<span class="bold">Instructions</span>
		</Header>

		<div class="body">
			@if (_currentStep == 1)
			{
				<div class="title-group">
					<p class="title">@i18n.TranslateText("Instructions.Conformity")</p>
					<p class="title-description">Merci de bien respecter ces instructions pour réussir votre photo officielle</p>
				</div>
				<img src="images/instructions/photo-logo.svg" class="title-logo" />
				<img src="images/instructions/instruction-image.png" class="instruction-img center-column" />
				<div class="ellipse center-column"></div>
				<img src="images/instructions/arrow-instruction.svg" class="arrow arrow-1 center-column" />
				<p class="arrow-text arrow-text-1 center-column">Gardez le visage, le front et les oreilles dégagés</p>
				<img src="images/instructions/arrow-instruction.svg" class="arrow arrow-2 center-column" />
				<p class="arrow-text arrow-text-2 center-column">Retirez vos lunettes, la tête doit être nue, sans chapeau, casquette, boucle d’oreille ni autre accessoire</p>
				<img src="images/instructions/arrow-instruction.svg" class="arrow arrow-3 center-column" />
				<p class="arrow-text arrow-text-3 center-column">Gardez les yeux ouverts, la tête droite, regardez droit devant vous sans sourire</p>
				<img src="images/instructions/arrow-instruction.svg" class="arrow arrow-4 center-column" />
				<p class="arrow-text arrow-text-4 center-column">Le cou doit être dégagé</p>
				<div class="wrong-image">
					<img src="images/instructions/wrong-image-1.png" class="wrong-img wrong-img-1" />
					<img src="images/instructions/wrong-image-2.png" class="wrong-img wrong-img-2" />
				</div>
			}
			else if (_currentStep == 2)
			{
				<div class="title-group">
					<p class="title">Fermez le rideau pour faire une photo conforme</p>
					<p class="title-description">
						Pour une photo d’identité conforme, merci de fermer le rideau. Trop de lumière altère le rendu
					</p>
				</div>
				<img src="images/instructions/curtain.png" class="curtain center-column" />
			}
			else if (_currentStep == 3)
			{
				<div class="title-group">
					<p class="title">Planche conforme</p>
					<p class="title-description">
						Pas de pliures, de taches, de déchirures nis d’écritures. <br />
						Toute altération peut rendre la photo non conforme aux exigences officielles.
					</p>
				</div>
				<img src="images/instructions/example-x6.png" class="example-x6 center-column" />
				<img src="images/instructions/arrow-instruction.svg" class="arrow arrow-5 center-column" />
				<p class="arrow-text arrow-text-5 center-column">Pas de trace, de saleté</p>
				<img src="images/instructions/arrow-instruction.svg" class="arrow arrow-6 center-column" />
				<p class="arrow-text arrow-text-6 center-column">Pas de pliure</p>
				<div class="finish-btn" @onclick="NavigateToCalibration">@i18n.TranslateText("Instructions.Understood") <i class="fa fa-arrow-right"></i></div>
			}			

			@if (_currentStep > 1)
			{
				<div class="white-circle left-circle" @onclick="MoveToPreviousStep">
					<img src="images/up-arrow.svg" class="left-arrow" />
				</div>
			}

			@if (_currentStep < _maxStep)
			{
				<div class="white-circle right-circle @_animateLoadingClass"
					 style="--animation-duration: @(_waitingDurationInMillisecond/1000)s;"
					 @onclick ="MoveToNextStep">
					<img src="images/up-arrow.svg" class="right-arrow" />
				</div>
			}

			<div class="instruction-steps center-column">
				@for (int i = 1; i <= _maxStep; i++)
				{
					if (i == _currentStep)
					{
						<div class="current-step"></div>
					}
					else
					{
						<div class="other-step"></div>
					}
				}
			</div>
		</div>
		<LangPicker></LangPicker>
	</CascadingValue>

	<DefaultFooter />
	<div id="overlay"></div>
</div>


