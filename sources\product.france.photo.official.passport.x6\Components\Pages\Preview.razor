﻿@page "/preview"
@using Kis.Framework.Ui.LocalizationTool
@using Kis.Framework.Ui.Components
@using Kis.Framework.Ui.Services
@inject ILocalization i18n
@inject NavigationService Navigation

@if (_showRetryConfirm)
{
    <Popup Title="@i18n.TranslateText("global.popup.title.startagain")"
           Message="@i18n.TranslateText("global.popup.message.3attempts")"
           OkText="@i18n.TranslateText("Preview.TryAgain")"
           CancelText="@i18n.TranslateText("Global.Cancel")"
           OkButtonColor="@Popup.OrangeButton"
           CancelButtonColor="@Popup.TransparentButton"
	       CancelButtonTextColor="@Popup.BlackText"
           OnOk="ConfirmToRetry"
           OnCancel="CancelRetry"
    />
}
<div class="container">
    @if (State.AnalysisResults.Count > 0)
    {
        <div class="picked-photo-title-container">
            @if (State.AnalysisResults[State.ChosenPicture].AnalyseResult)
            {
                <p id="text_photo_title" class="picked-photo-title success">
                    @i18n.TranslateText("Preview.PictureOk")
                </p>
                <p id="text_validate_or_change" class="picked-photo-title">
                    @i18n.TranslateText("Preview.ValidateOrChange")
                </p>
            }
            else
            {
                <p id="text_photo_title" class="picked-photo-title error">
                    @i18n.TranslateText("Preview.PictureKo")
                </p>
                <p id="text_validate_or_change" class="picked-photo-title">
                    @i18n.TranslateText("Preview.ValidateOrChange")
                </p>
            }
        </div>

        <div class="pictures-container">
            @for (var i = 0; i < State.Base64IcaoImages.Length; i++)
            {
                var j = i;
                var picture = State.Base64IcaoImages[i];
                if (string.IsNullOrEmpty(picture)) break;

                var cssClass = State.AnalysisResults[i].AnalyseResult ? "success" : "error";

                <div id="button_choose_picture" class="pictures-photo-container" @onclick="() => ChoosePicture(j)">
                    <div aria-invalid="false"
                         class="pictures-photo-container-presentation @(State.ChosenPicture == j ? cssClass : "")">
                        <div id="text_picture_title" class="pictures-photo-container-presentation-title @cssClass">
                            <p>
                                @if (cssClass == "success")
                                {
                                    <text>@i18n.TranslateText("Preview.PictureTitleOk")</text>
                                }
                                else
                                {
                                    <text>@i18n.TranslateText("Preview.PictureTitleKo")</text>
                                }
                            </p>
                        </div>
                        <img id="image_preview_photo" alt="preview" class="pictures-photo-container-preview-box"
                             src="data:image/bmp;base64,@picture"/>
                    </div>
                </div>
            }

            @if (State.RawImages.Count < 3)
            {
                <div class="pictures-photo-container">
                    <div id="button_take_new_picture" class="pictures-photo-container new"
                         @onclick="GoToLive">
                        <img id="image_camera" alt="camera" src="images/camera.svg" />
                        <p id="text_take_new_picture">
                            @i18n.TranslateText("Preview.TakeNewPicture")
                        </p>
                    </div>

                </div>
            }
        </div>
    }
    <div class="action-container">

        @if (CanRetry)
        {
        <button id="button_retry" class="btn retry d-none" @onclick="Retry">
            <img alt="arrow" src="images/black-undo.svg"/>
            @i18n.TranslateText("Preview.TryAgain")
        </button>
        }

        <button id="button_validate" class="btn validate"
                @onclick="ValidatePhoto">
            @i18n.TranslateText("Preview.ChoosePicture")
            <img alt="arrow" src="images/white-left-arrow.svg"/>
        </button>
    </div>
</div>