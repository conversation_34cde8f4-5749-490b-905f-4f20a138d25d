﻿using Kis.SoundManager;
using Microsoft.AspNetCore.Components;

namespace Kis.Product.France.Photo.Official.Passport.X6.Components.Pages;

public partial class Instructions
{
    [Parameter] public bool ShowCloseButton { get; set; }
    [Parameter] public EventCallback OnClose { get; set; }
    [Parameter] public bool PlayInstructionSounds { get; set; } = true;
    [CascadingParameter] public SoundPlayer Player { get; set; } = default!;

    protected override void OnAfterRender(bool firstRender)
    {
        if (firstRender)
        {
            if (PlayInstructionSounds)
                _ = Player.Play("IcaoInfoPopup");
        }
    }

    private async Task Close()
    {
        await OnClose.InvokeAsync();
    }
}