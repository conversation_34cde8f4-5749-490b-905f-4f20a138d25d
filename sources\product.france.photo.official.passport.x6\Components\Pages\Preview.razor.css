﻿button:disabled {
    background-color: #e0e0e0 !important;
    color: #9e9e9e !important;
}

.container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    border-radius: 30px;
    border: 4px solid white;
    background-color: var(--app-background);
    gap: 25px;
    padding: 45px 70px;
}

.picked-photo-title-container {
    display: flex;
    flex-direction: column;
    gap: 6px;
    width: 100%;
}

.picked-photo-title {
    font-family: Futura PT, serif;
    font-size: 28px;
    font-weight: 700;
    line-height: 34px;
    letter-spacing: -1px;
    text-align: center;
}

.picked-photo-title.success {
    color: #16C571
}

.picked-photo-title.error {
    color: #E3442F
}

.pictures-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    height: max-content;
    width: 100%;
    gap: 30px;
}

.pictures-photo-container {
    display: flex;
    flex-direction: column;
}

.pictures-photo-container.new {
    border: 3px dashed #E7E7E7;
    gap: 10px;
    height: 100%;
    grid-column: span 1;
    background-color: white;
    border-radius: 10px;
    overflow-y: hidden;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    display: flex;
    position: relative;
    cursor: pointer;
}

.pictures-photo-container.new img {
    background: linear-gradient(180deg, #ED7225 0%, #D64518 100%);
    width: 62px;
    padding: 16px;
    border-radius: 50%;
}

.pictures-photo-container.new p {
    font-family: Futura PT, serif;
    font-size: 28px;
    font-weight: 700;
    line-height: 30px;
    letter-spacing: -1px;
    text-align: center;
    width: 200px;
}

.pictures-photo-container-presentation {
    grid-column: span 1;
    border-radius: 10px;
    overflow-y: hidden;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    display: flex;
    position: relative;
    cursor: pointer;
    border: 6px solid transparent;
    height: 100%;
    width: 100%;
}

.pictures-photo-container-presentation.success {
    border: 6px solid #16C571
}

.pictures-photo-container-presentation.error {
    border: 6px solid #E3442F
}

.pictures-photo-container-presentation-title {
    padding: 8px 10px 8px 10px;
    position: absolute;
    top: 0;
    font-family: Roboto, serif;
    font-size: 18px;
    font-weight: 500;
    line-height: 16px;
    letter-spacing: 0;
    text-align: center;
    color: white;
    border-radius: 0 0 10px 10px
}

.pictures-photo-container-presentation-title.success {
    background-color: #16C571
}

.pictures-photo-container-presentation-title.error {
    background-color: #E3442F
}

.pictures-photo-container-preview-box {
    width: 100%;
    height: 100%;
}

.pictures-photo-label-container {
    margin-top: 10px;
}

.pictures-photo-label {
    font-family: Roboto, serif;
    font-size: 20px;
    font-weight: 700;
    line-height: 26px;
    letter-spacing: -1px;
    text-align: center;
    color: var(--app-background);
}

.pictures-photo-label.valid {
    color: #16C571;
}

.pictures-photo-label.invalid {
    color: #D64518;
}


.action-container {
    display: flex;
    gap: 30px
}

.btn {
    height: 100%;
    width: 100%;
    justify-content: center;
    border-radius: 60px;
    cursor: pointer;
    border: double 4px transparent;
    padding: 25px 50px 25px 50px;
    background-origin: border-box;
    background-clip: padding-box, border-box;
    font-family: Futura PT, serif;
    font-size: 28px;
    font-weight: 700;
    line-height: 30px;
    letter-spacing: -1px;
    display: flex;
    align-items: center;
    gap: 14px;
}

.btn.retry {
    background-image: linear-gradient(#D6D4CF, #D6D4CF), linear-gradient(180deg, #ED7225 0%, #D64518 100%);
}

.btn.validate {
    background-color: #16C571;
    color: white
}

@media (min-width: 1365px) and (max-width: 1367px) and (min-height: 767px) and (max-height: 769px) {
    .container {
        padding: 10px 70px;
    }

    .pictures-photo-container-preview-box {
        width: 50%;
        height: 100%;
    }
}

@media (min-width: 1919px) and (max-width: 1921px) and (min-height: 1079px) and (max-height: 1081px) {
    .pictures-photo-container-preview-box {
        width: 75%;
        height: 100%;
    }
}
