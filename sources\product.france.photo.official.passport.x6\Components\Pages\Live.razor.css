﻿.container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    height: 100%;
    border-radius: 30px;
    background-color: white;
}

.container-left {
    display: flex;
    flex-direction: column;
    grid-column: span 1;
    justify-content: space-between;
    padding: 30px;
}

.container-left-title {
    font-size: 30px;
    line-height: 40px;
    letter-spacing: -1px;
    text-align: left;
    width: 200px;
}

.container-lef-help {
    width: 162px;
    height: 210px;
}

.container-center {
    display: flex;
    justify-content: center;
    grid-column: span 2;
    position: relative;
}

.container-right {
    display: flex;
    flex-direction: column;
    grid-column: span 1;
    justify-content: space-between;
    align-items: center;
    padding: 30px;
}

.live-eyes-zone-help {
    position: absolute;
    font-size: 20px;
    font-weight: bold;
    max-width: 60%;
    width: 60%;
    height: 52px;
    line-height: 1.2;
    top: 55px;
    left: 6%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.live-image {
    height: 806px;
    width: 607px;
    object-fit: cover;
}

.live-image-marks {
    position: absolute;
    width: 100%;
    top: 55px;
}

.capture-button-container {
    cursor: pointer;
    position: absolute;
    bottom: 30px;
    width: 127px;
    height: 127px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.capture-button {
    position: absolute;
    z-index: 1;
    width: 100%;
    height: 100%;
}

.camera-icon {
    position: absolute;
    width: 54.28px;
    height: 41px;
    z-index: 2;
}

.tries-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.tries-container-title {
    font-family: Roboto, serif;
    font-size: 20px;
    font-weight: 700;
    line-height: 26px;
    letter-spacing: -1px;
    text-align: right;
}

.tries-container-body {
    padding: 23px 31px 23px 31px;
    border-radius: 10px;
    gap: 8px;
    background-color: var(--app-background);
}

.tries-from {
    background: linear-gradient(180deg, #ED7225 0%, #D64518 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-family: Futura PT, serif;
    font-size: 50px;
    font-weight: 700;
    line-height: 40px;
    letter-spacing: 0;
    text-align: center;
}

.tries-to {
    font-family: Roboto, serif;
    font-size: 20px;
    font-weight: 700;
    line-height: 26px;
    letter-spacing: -1px;
    text-align: center;
}

.controls-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
}

.controls-container-title {
    width: 120px;
    font-family: Roboto, serif;
    font-size: 20px;
    font-weight: 700;
    line-height: 26px;
    letter-spacing: -1px;
    text-align: center;
}

.controls-button {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 120px;
    height: 120px;
    border-radius: 70px;
    border: 3px solid #BBBBBB;
    background-color: var(--app-background);
    margin: auto 0 auto auto;
}

.controls-button button {
    width: 50px;
}

.img-stool {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 246px;
    height: 445px;
}

@media (min-width: 1365px) and (max-width: 1367px) and (min-height: 767px) and (max-height: 769px) {
    .live-image {
        height: 540px;
        width: 407px;
    }

    .live-image-marks {
        top: 36px;
        height: 513px;
        width: 390px;
    }

    .capture-button-container {
        height: 85px;
        width: 85px;
    }

    .camera-icon {
        width: 36.33px;
        height: 27.44px;
    }

    .container {
        display: flex;
        height: 100%;
        border-radius: 30px;
        background-color: white;
    }

    .container-left {
        align-items: center;
        width: 30vw;
        padding: 30px;
    }

    .container-right {
        width: 30vw;
        align-items: center;
        padding: 30px;
    }

    .container-left-title {
        font-size: 30px;
        line-height: 40px;
        letter-spacing: -1px;
        text-align: center;
        width: 100%;
    }

    .tries-container-title {
        font-size: 20px;
        font-weight: 700;
        line-height: 26px;
        letter-spacing: -1px;
        text-align: center;
    }

    .live-eyes-zone-help {
        max-width: 100%;
        width: 100%;
        left: 0;
    }

    .controls-container {
        gap: 7px;
    }

    .controls-button {
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100px;
        height: 100px;
        border-radius: 70px;
        border: 3px solid #BBBBBB;
        background-color: var(--app-background);
        margin: auto auto auto auto;
    }

    .img-stool {
        width: 143px;
        height: 260px;
        margin-left: 20px;
    }
}

@media (min-width: 1919px) and (max-width: 1921px) and (min-height: 1079px) and (max-height: 1081px) {

    .live-image {
        width: 633px;
        height: auto;
    }

    .live-image-marks {
        top: 5vh;
        width: 96%;
    }

    .capture-button-container {
        bottom: 45px;
    }

    .container {
        display: flex;
        height: 100%;
        border-radius: 30px;
        background-color: white;
        justify-content: space-between;
    }

    .container-left {
        align-items: center;
        width: 30vw;
        padding: 30px;
    }

    .container-right {
        width: 30vw;
        align-items: center;
        padding: 30px;
    }

    .container-left-title {
        font-size: 36px;
        line-height: 42px;
        width: 100%;
        text-align: center;
    }

    .tries-container-title {
        font-size: 30px;
        line-height: 35px;
        width: 100%;
    }

    .live-eyes-zone-help {
        max-width: 100%;
        width: 100%;
        left: 0;
        font-size: 33px;
        top: 33%;
    }

    .container-lef-help {
        width: auto;
        height: 404px;
    }

    .controls-container-title {
        width: 230px;
        font-size: 35px;
        line-height: 35px
    }

    .controls-button {
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 120px;
        height: 120px;
        border-radius: 70px;
        border: 3px solid #BBBBBB;
        background-color: var(--app-background);
        margin: auto auto auto auto;
    }
}
