﻿.container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: var(--app-background);
    padding: 75px 60px 50px 60px;
    transition: none;
    position: fixed;
    gap: 30px;
}

.title {
    text-align: center;
    margin: 0;
}

.body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding: 50px;
    height: 100%;
    border-radius: 30px;
    border: 4px solid white;
}

.spinner {
    margin-bottom: 10%;
}

.body-title {
    width: 70%;
    font-family: Futura PT, serif;
    font-size: 58px;
    font-weight: 700;
    line-height: 64px;
    letter-spacing: -2px;
    text-align: center;
    margin-top: 25%;
}

.footer-img {
    height: 25px;
}

@media (min-width: 1365px) and (max-width: 1367px) and (min-height: 767px) and (max-height: 769px) {
    .body-title {
        margin-top: 3%;
    }
}

@media (min-width: 1919px) and (max-width: 1921px) and (min-height: 1079px) and (max-height: 1081px) {
    .body-title {
        margin-top: 14%;
    }
}