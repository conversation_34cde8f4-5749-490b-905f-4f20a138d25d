﻿using System.Globalization;
using Kis.Framework.Basic.ProjectInformation.Configuration.Localization;
using Microsoft.AspNetCore.Components;

namespace Kis.Product.France.Photo.Official.Passport.X6.Components;

public partial class LangPicker
{
    private string GetFlag(string language) => "images/lang/" + language + ".png";

    [Inject] private LocalizationConfigurationData OperatorLocalization { get; set; } = default!;

    private List<CultureInfo> Languages { get; } = [];

    protected override void OnInitialized()
    {
        var allLanguages = i18n.SupportedLanguages.ToList();

        Languages.Insert(0, i18n.CurrentLanguage);

        foreach (var lang in OperatorLocalization.OperatorCulture.Languages)
        {
            var language = i18n.SupportedLanguages.FirstOrDefault(x => x.Name == lang);
            if (language == null)
                continue;
            if (Languages.Contains(language))
                continue;
            Languages.Add(language);
        }

        foreach (var lang in i18n.SupportedLanguages.Where(l => !Languages.Contains(l)).OrderBy(m => m.NativeName))
        {
            Languages.Add(lang);
        }

        base.OnInitialized();
    }

    private void SetLanguage(CultureInfo cultureInfo)
    {
        i18n.CurrentLanguage = cultureInfo;
        NavigationManager.Refresh(true);
    }
}