﻿@page "/countdown"
@using Kis.Framework.Ui.LocalizationTool
@layout EmptyLayout
@inject ILocalization i18n


<div class="container">
    <div class="countdown">
        @if (!WaitingSpinnerIsHidden)
        {
            <header>
                <h1 id="text_please_wait" class="title">
                    @i18n.TranslateText("Global.PleaseWait")
                </h1>
            </header>
            <div class="body">
                <h3 id="text_conformity_check" class="body-title">
                    @i18n.TranslateText("TakePicture.ConformityCheck")
                </h3>
                <img id="image_spinner" alt="logo" class="spinner" src="images/spinner.svg" />
            </div>
        }
        else
        {
            <header>
                <h1 id="text_take_picture" class="title">
                    <span>@i18n.TranslateText("TakePicture.Title")&nbsp;@(State.CurrentPicture + 1)</span>
                </h1>
            </header>
            <div class="body">
                <p id="text_count_down" class="count @(CountdownIsHidden? "hidden" : string.Empty)">
                    @_countdown
                </p>
                <p id="text_look_straight_ahead" class="notice">
                    @i18n.TranslateText("Common.LookStraightAhead")
                </p>
            </div>
        }
    </div>
</div>


<div class="black-screen" hidden="@BlackScreenIsHidden" />

