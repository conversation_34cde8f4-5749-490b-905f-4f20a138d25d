﻿.container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: var(--app-background);
    padding: 75px 60px 50px 60px;
    transition: none;
    position: fixed;
    gap: 30px;
}

.title {
    text-align: center;
    margin: 0;
}

.body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-evenly;
    padding: 50px;
    height: 100%;
    border-radius: 30px;
    border: 4px solid white;
}

.body-title {
    width: 70%;
    font-family: Futura PT, serif;
    font-size: 58px;
    font-weight: 700;
    line-height: 64px;
    letter-spacing: -2px;
    text-align: center;
}

.footer-img {
    height: 25px;
}