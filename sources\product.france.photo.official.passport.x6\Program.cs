using Kis.Framework.Basic.ProjectInformation;
using Kis.Framework.Ui.LocalizationTool;
using Kis.Framework.Ui.Toolkit.Extensions;
using Kis.Product.France.Photo.Official.Passport.X6.Components;
using Kis.Product.France.Photo.Official.Passport.X6.Services;
using Kis.Product.France.Photo.Official.Passport.X6.Setup;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Extensions.FileProviders;

var builder = WebApplication.CreateBuilder(args);

// Add NLog from extension in Kis.Framework.Ui
builder.AddLogger();

// Add services to the container.
builder.Services.AddRazorComponents()
	.AddInteractiveServerComponents(opts => opts.DetailedErrors = builder.Environment.IsDevelopment());

builder.Services
	.AddServiceSpecificObjects();

builder.Services.AddResponseCompression(options => { options.Providers.Add<BrotliCompressionProvider>(); });

var app = builder.Build();

// Configure the HTTP request pipeline.
//if (!app.Environment.IsDevelopment())
//{
app.UseExceptionHandler(b =>
{
	b.Run(r =>
	{
		var state = b.ApplicationServices.CreateScope().ServiceProvider.GetRequiredService<AppState>();
		r.Response.Redirect(state.ErrorReturnUrl + "?service=Passeport");
		return Task.CompletedTask;
	});
});
//}

var pathProvider = app.Services.GetRequiredService<IPathProvider>();
_ = app.Services.GetRequiredService<ILocalization>(); // Ensure localization is loaded
app.UseStaticFiles();
app.UseStaticFiles(new StaticFileOptions
{
	FileProvider = new PhysicalFileProvider(pathProvider.GetOperatorFolder()),
	RequestPath = "/operator"
});
app.UseAntiforgery();

app.MapRazorComponents<App>()
	.AddInteractiveServerRenderMode();

app.ConfigureMinimalApi();

app.Run();