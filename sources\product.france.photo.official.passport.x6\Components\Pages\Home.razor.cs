using Kis.Framework.Basic.ProjectInformation;
using Kis.Framework.Basic.ProjectInformation.Configuration.Metadata;
using Kis.Framework.Ui.LocalizationTool;
using Kis.Framework.Ui.Services;
using Kis.Library.Data;
using Kis.Product.France.Photo.Official.Passport.X6.Services;
using Kis.Share.Model.Hardware;
using Kis.SoundManager;
using Microsoft.AspNetCore.Components;
using System.Timers;
using System.Web;
using Timer = System.Timers.Timer;

namespace Kis.Product.France.Photo.Official.Passport.X6.Components.Pages;

public partial class Home : IAsyncDisposable
{
	[SupplyParameterFromQuery(Name = "s")] public string? SuccessReturnUrl { get; set; }
	[SupplyParameterFromQuery(Name = "e")] public string? ErrorReturnUrl { get; set; }
	[SupplyParameterFromQuery(Name = "c")] public string? CancelReturnUrl { get; set; }
	[SupplyParameterFromQuery(Name = "t")] public string? TimeoutUrl { get; set; }
	[SupplyParameterFromQuery(Name = "g")] public string? GalleryId { get; set; }
	[SupplyParameterFromQuery(Name = "l")] public string? CultureInfoName { get; set; } = "fr-FR";
	[Inject] private NavigationService Navigation { get; set; } = default!;
	[Inject] private AppState State { get; set; } = default!;
	[Inject] private ILocalization Localization { get; set; } = default!;
	[Inject] private IKisData KisData { get; set; } = default!;
    [Inject] private IPathProvider PathProvider { get; set; } = default!;
    [Inject] private KisHardwareInformationParser HardwareInformation { get; set; } = default!;
    [CascadingParameter] public SoundPlayer Player { get; set; } = default!;
    private Metadata _metadata;

    private int _currentStep = 1;
	private int _maxStep = 3;
    private string _animateLoadingClass = "loading-animation";
	private int _waitingDurationInMillisecond = 5000;
    private Timer? _timer;

    protected override void OnInitialized()
	{
		State.Reset();
		State.CancelReturnUrl = HttpUtility.UrlDecode(CancelReturnUrl) ?? "";
		State.SuccessReturnUrl = HttpUtility.UrlDecode(SuccessReturnUrl) ?? "";
		State.ErrorReturnUrl = HttpUtility.UrlDecode(ErrorReturnUrl) ?? "";
		State.TimeoutUrl = HttpUtility.UrlDecode(TimeoutUrl) ?? "";

		if (!string.IsNullOrEmpty(GalleryId) && Guid.TryParse(GalleryId, out var id))
		{
			State.GalleryGuid = id;
		}
		else
		{
			//remove this when we know how we handle the error
			State.GalleryGuid = KisData.CreateMediaGallery();
		}

		if (!string.IsNullOrEmpty(CultureInfoName))
		{
			Localization.CurrentLanguage = new(CultureInfoName);
		}

        _metadata = PathProvider.GetMetadata();
        GetMachineModel();
    }

    protected override void OnAfterRender(bool firstRender)
    {
        if (firstRender)
		{
			_ = Player.Play("IcaoInfoPopup");

			_timer = new Timer(_waitingDurationInMillisecond)
			{
				AutoReset = true,
				//Enabled = true
			};
			_timer.Elapsed += TimerElapsed;
		}
	}

	private async Task MoveToPreviousStep()
	{
        _currentStep--;
		await StepHasChanged();
    }

    private async Task MoveToNextStep()
	{
        _currentStep++;
        await StepHasChanged();
		if (_currentStep > _maxStep)
			NavigateToCalibration();
    }

	private async Task StepHasChanged()
    {
		_timer?.Stop();
        _animateLoadingClass = "";
        StateHasChanged();

        await Task.Delay(20);

        //_timer?.Start();
        _animateLoadingClass = "loading-animation";
        StateHasChanged();
    }

	private void NavigateToCalibration()
	{
        Navigation.NavigateTo("/calibration");
    }

	private void TimerElapsed(object? sender, ElapsedEventArgs e)
	{
		InvokeAsync(MoveToNextStep);
	}

    public void GetMachineModel()
    {
        if (HardwareInformation.Data.Camera.FixedMachineModels.Contains(_metadata.MachineModel))
        {
            if (_metadata.MachineModel == "PmrLight_NextGenBooth")
            {
                State.MachineType = MachineType.FIXED_CAMERA_STOOL;
            }
            else
            {
                State.MachineType = MachineType.FIXED_CAMERA;
            }
        }
        else
        {
            State.MachineType = MachineType.FIXED_STOOL;
        }
    }

    public ValueTask DisposeAsync()
	{
		if (_timer is not null)
		{
			_timer.Elapsed -= TimerElapsed;
			_timer.Dispose();
		}
		return ValueTask.CompletedTask;
	}
}