﻿using Kis.Library.HardwareCheck;
using Kis.Product.France.Photo.Official.Passport.X6.Services;

namespace Kis.Product.France.Photo.Official.Passport.X6.Setup
{
	public static class MinimalApiSetup
	{
		public static void ConfigureMinimalApi(this WebApplication app)
		{
			app.MapGet("/requirements", async (KisHardwareCheck hardwareCheck) =>
			{
				var checkResult = await hardwareCheck.Check(new()
				{
					Camera = true,
					IoBoard = true,
					Payment = false,
					Printer = new()
					{
						Availability = true,
						SheetFormats =
						[
							new PrinterFormatQuantity()
							{
								Format = "04x06",
								Quantity = 1
							}
						]
					},
					TicketPrinter = false
				});

				//Check if all the hardware are available 
				var available = checkResult is { Camera: true, IoBoard: true, Printer: true };

				return TypedResults.Ok(available);
			});
            app.MapGet("/health", () => Results.Json(new
            {
                productID = AppState.ProductName,
                status = "healthy"
            }));
		}
	}
}
