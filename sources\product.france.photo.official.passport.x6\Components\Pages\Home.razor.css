﻿:root {
    --animation-duration: 2s;
}

.container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: var(--app-background);
    padding: 75px 60px 0px 60px;
    transition: none;
    position: fixed;
    gap: 30px;
}

.body {
    background: #EF722533;
    border-radius: 20px;
    border: 2px solid #D64518;
    position: relative;
    height: 100%;
    /*border-image-source: linear-gradient(180deg, #ED7225 0%, #D64518 100%);*/
}

.title-group {
    padding-top: 34px;
    align-content: center;
}

.title {
    font-family: 'Futura PT', sans-serif;
    font-weight: 700;
    font-size: 40px;
    line-height: 40px;
    letter-spacing: -1px;
    text-align: center;
    color: #D64518;
}

.center-column {
    left: 50%;
    transform: translateX(-50%);
}

.title-description {
    font-family: 'Roboto', sans-serif;
    font-weight: 600;
    font-size: 24px;
    line-height: 32px;
    letter-spacing: -1px;
    text-align: center;
    margin-top: 5px;
}

.title-logo {
    position: absolute;
    top: 15px;
    right: 77px;
}

.instruction-img {
    position: absolute;
    width: 335px;
    height: 443px;
    top: 170px;
    box-shadow: 5px 5px 30px rgba(0, 0, 0, 0.05);
    border-radius: 20px;
}

.ellipse {
    position: absolute;
    width: 168px;
    height: 244px;
    top: 265px;
    margin-left: -10px;
    /*left: calc(50%);*/
    opacity: 1;
    border-width: 1.5px;
    border-radius: 50%;
    border: 1.5px solid #EDEAE5;
    z-index: 1
}

.arrow {
    position: absolute;
    opacity: 1;
}

.arrow-text {
    font-family: Roboto;
    font-weight: 500;
    font-size: 18px;
    line-height: 24px;
    letter-spacing: 0px;
    vertical-align: middle;
    opacity: 1;
    position: absolute;
}

.arrow-1 {
    width: 147px;
    top: 300px;
    margin-left: 112px;
}

.arrow-text-1 {
    width: 190px;
    top: 267px;
    margin-left: 300px;
}

.arrow-2 {
    width: 112px;
    top: 427px;
    margin-left: 125px;
}

.arrow-text-2 {
    width: 190px;
    top: 371px;
    margin-left: 300px;
}

.arrow-3 {
    width: 137px;
    top: 360px;
    margin-left: -193px;
    transform: scaleX(-1);
}

.arrow-text-3 {
    width: 190px;
    top: 326px;
    margin-left: -300px;
    text-align: right;
}

.arrow-4 {
    width: 137px;
    top: 523px;
    margin-left: -195px;
    transform: scaleX(-1);
}

.arrow-text-4 {
    width: 190px;
    top: 515px;
    margin-left: -300px;
    text-align: right;
}

.arrow-5 {
    width: 80px;
    top: 263px;
    left: 250px;
    transform: scaleX(-1);
}

.arrow-text-5 {
    width: 184px;
    top: 256px;
    left: 54px;
    text-align: right;
}

.arrow-6 {
    width: 122px;
    top: 491px;
    left: 771px;
}

.arrow-text-6 {
    width: 123px;
    top: 484px;
    left: 909px;
}

.curtain {
    position: absolute;
    width: 335px;
    height: 443px;
    top: 170px;
    box-shadow: 5px 5px 30px rgba(0, 0, 0, 0.05);
    border-radius: 20px;
}

.example-x6 {
    position: absolute;
    width: 568px;
    height: 378px;
    top: 184px;
    filter: drop-shadow(7px 4px 10px rgba(0, 0, 0, 0.25));
}

.finish-btn {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 40px 50px;
    gap: 14px;
    position: absolute;
    width: 370px;
    height: 100px;
    right: 32px;
    bottom: 31px;
    background: linear-gradient(180deg, #ED7225 0%, #D64518 100%);
    box-shadow: 5px 5px 30px rgba(0, 0, 0, 0.05);
    border-radius: 60px;

    font-family: 'Futura PT';
    font-style: normal;
    font-weight: 700;
    font-size: 28px;
    line-height: 30px;
    letter-spacing: -1px;
    color: #FFFFFF;
}

.white-circle {
    position: absolute;
    top: 316px;
    width: 120px;
    height: 120px;
    background-color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.5s ease-in-out;
}

.loading-animation::before {
    content: "";
    position: absolute;
    box-sizing: border-box;
    inset: -8px;
    border-radius: 50%;
    border: 8px solid #FF3D00;
    animation: prixClipFix var(--animation-duration) linear;
}

@keyframes prixClipFix {
    0% {
        clip-path: polygon(50% 50%, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%);
    }

    20% {
        clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 0%, 100% 0%, 100% 0%, 100% 0%);
    }

    40% {
        clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 100% 100%, 100% 100%, 100% 100%);
    }

    60% {
        clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 0% 100%, 0% 100%, 0% 100%);
    }

    80% {
        clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 0% 100%, 0% 0%, 0% 0%);
    }

    100% {
        clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 0% 100%, 0% 0%, 50% 0%);
    }
}

.left-circle {
    left: 37px;
}

.right-circle {
    right: 37px;
}

.left-arrow {
    transform: rotate(270deg);
}

.right-arrow {
    transform: rotate(90deg);
}

.wrong-image {
    position: absolute;
    top: 535px;
    right: 31px;
    display: flex;
    gap: 12px;
}

.instruction-steps {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: 0px;
    gap: 10px;
    position: absolute;
    width: 120px;
    height: 10px;
    top: 680px;
}

.current-step {
    width: 60px;
    height: 10px;
    background: linear-gradient(180deg, #ED7225 0%, #D64518 100%);
    border-radius: 10px;
    align-self: stretch;
    flex-grow: 0;
}

.other-step {
    width: 20px;
    height: 10px;
    background: #E7E7E7;
    border-radius: 10px;
}

@media (min-width: 1365px) and (max-width: 1367px) and (min-height: 767px) and (max-height: 769px) {
    .container {
        padding: 60px 60px 0px 60px;
    }

    .title-group {
        padding-top: 24px;
    }

    .title {
        font-size: 35px;
        line-height: 35px;
    }

    .title-description {
        font-size: 24px;
        line-height: 24px;
    }

    .title-logo {
        top: 15px;
        right: 77px;
        width: 100px;
        height: 100px;
    }

    .instruction-img {
        width: 251px;
        height: 332px;
        top: 110px;
        border-radius: 15px;
    }

    .ellipse {
        width: 125px;
        height: 185px;
        top: 180px;
        margin-left: -7px;
    }

    .arrow-text {
        font-size: 17px;
        line-height: 22px;
    }

    .arrow-1 {
        top: 200px;
        margin-left: 95px;
    }

    .arrow-text-1 {
        top: 180px;
        margin-left: 280px;
    }

    .arrow-2 {
        top: 300px;
        margin-left: 110px;
    }

    .arrow-text-2 {
        top: 270px;
        margin-left: 300px;
        margin-left: 280px;
    }

    .arrow-3 {
        top: 250px;
        margin-left: -180px;
    }

    .arrow-text-3 {
        top: 215px;
        margin-left: -300px;
    }

    .arrow-4 {
        top: 370px;
        margin-left: -180px;
    }

    .arrow-text-4 {
        top: 365px;
    }

    .arrow-5 {
        top: 180px;
        margin-left: 110px;
    }

    .arrow-text-5 {
        top: 173px;
        margin-left: 200px;
    }

    .arrow-6 {
        top: 360px;
        margin-left: 50px;
    }

    .arrow-text-6 {
        top: 355px;
        margin-left: 50px;
    }

    .curtain {
        width: 251px;
        height: 332px;
        top: 110px;
        border-radius: 15px;
    }

    .example-x6 {
        width: 426px;
        height: 283px;
        top: 140px;
    }

    .finish-btn {
        bottom: 10px;
        right: 10px;
        width: 300px;
        height: 50px;
    }

    .white-circle {
        top: 200px;
        width: 90px;
        height: 90px;
    }

    .left-circle {
        left: 25px;
    }

    .right-circle {
        right: 25px;
    }

    .wrong-image {
        top: 360px;
        height: 130px;
        right: 20px;
        display: flex;
        gap: 12px;
    }

    .instruction-steps {
        top: 470px;
    }
}

@media (min-width: 1919px) and (max-width: 1921px) and (min-height: 1079px) and (max-height: 1081px) {
    .wrong-image {
        bottom: 50px;
        right: 31px;
    }

    .example-x6 {
        width: 624px;
        height: 415px;
        top: 184px;
    }

    .arrow-5 {
        top: 250px;
        margin-left: 280px;
        width: 100px;
    }

    .arrow-text-5 {
        top: 240px;
        margin-left: 370px;
    }

    .arrow-6 {
        top: 510px;
        margin-left: 400px;
    }

    .arrow-text-6 {
        top: 505px;
        margin-left: 400px;
    }

    .finish-btn {
        bottom: 60px;
        right: 40px;
    }
}
