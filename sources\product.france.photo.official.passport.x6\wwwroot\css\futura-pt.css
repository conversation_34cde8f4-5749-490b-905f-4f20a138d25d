@font-face {
    font-family: 'futura pt';
    font-style: normal;
    font-weight: 400;
    src: local('Futura PT'), url('../fonts/FuturaPTBook.otf') format('opentype');
}

@font-face {
    font-family: 'futura pt';
    font-style: normal;
    font-weight: 300;
    src: local('Futura PT'), url('../fonts/FuturaPTLight.otf') format('opentype');
}

@font-face {
    font-family: 'futura pt';
    font-style: normal;
    font-weight: 450;
    src: local('Futura PT'), url('../fonts/FuturaPTMedium.otf') format('opentype');
}

@font-face {
    font-family: 'futura pt';
    font-style: normal;
    font-weight: 500;
    src: local('Futura PT'), url('../fonts/FuturaPTDemi.otf') format('opentype');
}

@font-face {
    font-family: 'futura pt';
    font-style: normal;
    font-weight: 600;
    src: local('Futura PT'), url('../fonts/FuturaPTHeavy.otf') format('opentype');
}

@font-face {
    font-family: 'futura pt';
    font-style: normal;
    font-weight: 700;
    src: local('Futura PT'), url('../fonts/FuturaPTBold.otf') format('opentype');
}

@font-face {
    font-family: 'futura pt';
    font-style: normal;
    font-weight: 800;
    src: local('Futura PT'), url('../fonts/FuturaPTExtraBold.otf') format('opentype');
}
