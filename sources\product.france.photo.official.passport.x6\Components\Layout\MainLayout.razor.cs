﻿using Kis.Framework.Ui.Services;
using Kis.Product.France.Photo.Official.Passport.X6.Services;
using Kis.SoundManager;
using Microsoft.AspNetCore.Components;

namespace Kis.Product.France.Photo.Official.Passport.X6.Components.Layout;

public partial class MainLayout
{
    [Inject] private AppState State { get; set; } = default!;
    [Inject] private TimeoutService TimeoutService { get; set; } = default!;
    [Inject] private ILogger<MainLayout> Logger { get; set; } = default!;
    [Inject] private IConfiguration Configuration { get; set; } = default!;
    [Inject] private NavigationManager NavigationManager { get; set; } = default!;

    private SoundPlayer? _player;
    private bool _showTimeoutWarning;

    private SoundPlayer? Player
    {
        get => _player;
        set
        {
            _player = value;
            if (value is not null)
                StateHasChanged();
        }
    }

    private bool _showInstructions = false;
    private bool _showCloseModal = false;

    private async Task ShowCloseModal()
    {
        _showCloseModal = true;
        await InvokeAsync(StateHasChanged);
    }

    private async Task HideCloseModal()
    {
        _showCloseModal = false;
        await InvokeAsync(StateHasChanged);
    }

    private async Task ShowIntructions()
    {
        _showInstructions = true;
        await InvokeAsync(StateHasChanged);
    }

    private async Task HideInstructions()
    {
        _showInstructions = false;
        await (Player?.Stop() ?? Task.CompletedTask);
        await InvokeAsync(StateHasChanged);
    }

    private void ExitProduct()
    {
        Navigation.NavigateTo(State.CancelReturnUrl, true);
    }

    private async Task ResetTimeout()
    {
        if (_showTimeoutWarning)
        {
            Logger.LogInformation("User confirm still here");
            _showTimeoutWarning = false;
            Logger.LogInformation("Start first step timer");
            TimeoutService.ResetTimer();
            await InvokeAsync(StateHasChanged);
        }
        else
        {
            TimeoutService.ResetTimer();
        }
    }

    protected override void OnInitialized()
    {
        RegisterTimeoutHandler();
    }

    private void RegisterTimeoutHandler()
    {
        TimeoutService.OnTimerElasped = async firstTimeout =>
        {
            if (!TimeoutService.IsTimerEnabled) return;
            _showCloseModal = false;
            _showInstructions = false;
            if (firstTimeout)
            {
                Logger.LogInformation("First step timeout elapsed, ask user if still here");
                _showTimeoutWarning = true;
                await InvokeAsync(StateHasChanged);
            }
            else
            {
                Logger.LogInformation("Second step timeout elapsed, going to ATL");
                NavigationManager.NavigateTo(State.TimeoutUrl);
            }
        };
    }

    protected override void OnAfterRender(bool firstRender)
    {
        base.OnAfterRender(firstRender);

        TimeoutService.CreateTimer(Configuration.GetValue<int>("TimeoutInSeconds"));
    }
    
}