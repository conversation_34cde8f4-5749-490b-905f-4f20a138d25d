@using Kis.Framework.Ui.Components
@using Kis.Framework.Ui.LocalizationTool
@using Kis.Framework.Ui.Services
@using Kis.SoundManager
@inject ILocalization i18n
@inject AppState State
@inject NavigationService Navigation
@inject IJSRuntime JS

<div class="header">
    <div id="button_open_lang" class="button-round" @onclick="OpenLangDrawer">
        <img id="image_flag" alt="flag" class="button-icon" src="@_flag" />
    </div>
    <div>
        <h1 class="title">
            @ChildContent
        </h1>
    </div>
    @if (ShowCartButton)
    {
        <div id="button_open_cart" class="p-relative" @onclick="@(() => Navigation.NavigateTo("/cart"))">
            <div class="button-round cart">
                <span id="text_count">
                    @State.ItemsInCart.Count
                </span>
            </div>
            <div class="button-round">
                <img id="image_cart" alt="flag" class="button-icon" src="images/cart.svg" />
            </div>
        </div>
    }
</div>

@code {
    [Parameter, EditorRequired] public RenderFragment ChildContent { get; set; }
    [Parameter] public bool ShowCartButton { get; set; } = true;
    [CascadingParameter] public SoundPlayer Player { get; set; } = default!;

    private string _flag = "images/lang/fr-FR.png";
    private bool _openingLang;

    protected override void OnInitialized()
    {
        _flag = $"images/lang/{i18n.CurrentLanguage.Name}.png";
    }

    private async Task OpenLangDrawer()
    {
        if (_openingLang) return;
        try
        {
            _openingLang = true;
            await Player.Play("Language", () => ValueTask.CompletedTask);
            await JS.InvokeVoidAsync("onOpenLangDrawer");
        }
        finally
        {
            _openingLang = false;
        }
    }

}