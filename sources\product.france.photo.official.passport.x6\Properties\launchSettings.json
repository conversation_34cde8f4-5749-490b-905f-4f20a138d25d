{"profiles": {"http": {"commandName": "Project", "launchBrowser": true, "launchUrl": "http://localhost:5068/?l=en-GB", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:5068"}, "https": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7037;http://localhost:5068"}}, "$schema": "http://json.schemastore.org/launchsettings.json"}