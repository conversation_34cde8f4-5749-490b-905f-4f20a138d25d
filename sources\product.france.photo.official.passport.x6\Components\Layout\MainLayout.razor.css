.footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.footer-img {
    height: 25px;
}

.container {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 60px 60px 25px 60px;
    background-color: var(--app-background);
    z-index: 1;
    position: fixed;
    flex-direction: column;
    gap: 20px
}

.container-header {
    display: flex;
    justify-content: space-between;
}

.button-quit {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: black;
    width: 170px;
    height: 70px;
    padding: 20px;
    border-radius: 65px;
    gap: 6px;
    border: 0;
    background-color: white;
    font-size: 20px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: -1px;
    text-align: left;
    color: black;
    letter-spacing: -1px;
    font-family: 'Futura PT', serif;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 20px
}

.breadcrumb-step-container {
    display: flex;
    gap: 10px;
    align-items: center;
}

.breadcrumb-step {
    width: 36px;
    height: 36px;
    padding: 10px;
    border-radius: 30px;
    background: #E7E7E7;
    font-family: Roboto, serif;
    font-size: 18px;
    font-weight: 500;
    line-height: 16px;
    letter-spacing: 0;
    text-align: center;
}

.breadcrumb-step.active {
    background: linear-gradient(180deg, #ED7225 0%, #D64518 100%);
    color: white;    
}

.breadcrumb-step-label {
    font-size: 18px;
    font-weight: 450;
    line-height: 20px;
    letter-spacing: 0;
    text-align: center;
    color: #6C6C6C;
}

.breadcrumb-step-label.active {
    font-weight: 500;
    color: black;
}

.breadcrumb-icon {
    width: 24px;
    height: 24px;
}

.instructions-btn {
    cursor: pointer;
    padding: 12px 20px 12px 20px;
    border-radius: 50px;
    background: linear-gradient(180deg, #ED7225 0%, #D64518 100%);
    color: white;
    border: 0;
    font-weight: 500;
    line-height: 26px;
    text-align: left;
    width: 170px;
    height: 70px;
    display: flex;
    gap: 10px;
    align-items: center;
    font-size: 20px;
    letter-spacing: -1px;
    font-family: 'Futura PT', serif;
}

.instructions-btn-icon {
    width: 30px;
    height: 30px;
}

.instructions-container {
    width: 100%;
    height: 80%;
    position: fixed;
    z-index: 2;
    gap: 30px;
    bottom: 0;
}

.instructions-container-header {
    padding: 10px 70px 10px 70px;
    display: flex;
    justify-content: end;
}

.instructions-container-close-btn {
    cursor: pointer
}

.overlay {
    z-index: 2;
    position: fixed;
    background-color: black;
    opacity: 80%;
    width: 100vw;
    height: 100%;
    top: 0;
    animation: fade-in 1s ease forwards;
}

@keyframes fade-in {
    from {
        opacity: 0;
    }

    to {
        opacity: 80%;
    }
}

@media (min-width: 1365px) and (max-width: 1367px) and (min-height: 767px) and (max-height: 769px) {
    .instructions-container {
        height: 90%;
    }
}

@media (min-width: 1919px) and (max-width: 1921px) and (min-height: 1079px) and (max-height: 1081px) {

    .breadcrumb-step-label {
        font-size: 25px;
        line-height: 30px;
    }
}
