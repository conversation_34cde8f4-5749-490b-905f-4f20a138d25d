﻿@page "/production"
@using Kis.Framework.Ui.LocalizationTool
@inject ILocalization i18n
@layout EmptyLayout

<div class="container">
    <header>
        <h1 id="text_please_wait" class="title">
            @i18n.TranslateText("Global.PleaseWait")
        </h1>
    </header>
    <div class="body">
        <h3 id="text_product_is_processing" class="body-title">
            @i18n.TranslateText("Production.ProductIsProcessing")
        </h3>
        <img id="image_spinner" alt="logo" class="spinner" src="images/spinner.svg" />
    </div>
    <DefaultFooter />
</div>
