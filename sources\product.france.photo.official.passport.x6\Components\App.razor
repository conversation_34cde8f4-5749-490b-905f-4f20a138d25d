<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <link href="css/font-awesome.min.css?v=@_timestamp" rel="stylesheet" type="text/css" />
    <link href="css/futura-pt.css?v=@_timestamp" rel="stylesheet" type="text/css" />
    <link href="css/roboto.css?v=@_timestamp" rel="stylesheet" type="text/css" />
    <link href="css/inter.css?v=@_timestamp" rel="stylesheet" type="text/css" />
    <link href="css/app.css?v=@_timestamp" rel="stylesheet" type="text/css" />
    <link href="product.france.photo.official.passport.x6.styles.css?v=@_timestamp" rel="stylesheet" type="text/css" />

    <HeadOutlet @rendermode="InteractiveServer" />
</head>

<body style="--app-cursor: @_appCursor">
    <Routes @rendermode="@(new InteractiveServerRenderMode(prerender: false))" />
    <script src="_framework/blazor.web.js"></script>
    <script src="js/app.js"></script>
</body>

</html>

@code {
    [Inject] private IConfiguration Configuration { get; set; } = default!;
    private string _appCursor = "auto";
    private string _timestamp = "";

    protected override void OnInitialized()
    {
        // Append timestamp to URL to prevent caching of CSS files
        _timestamp = DateTime.UtcNow.Subtract(DateTime.UnixEpoch).TotalMilliseconds.ToString("F0");
        // Hide cursor if "ShowCursor" setting is set to false
        _appCursor = Configuration.GetValue<bool>("ShowCursor", true) ? "auto" : "none";
    }
}