﻿@using Kis.Framework.Ui.LocalizationTool
@inject ILocalization i18n

<div class="container">
    <div class="content">
        <div class="body">
            <div class="img-container">
                <img id="image_on_boarding" alt="onboarding" class="img" src="images/onboarding-2.png" />
                <div class="notice-container left-notice notice-1">
                    <p id="text_clear_face">
                        @i18n.TranslateText("Instructions.ClearFace")
                    </p>
                    <img id="image_union_1" alt="union" src="images/union.svg" />
                </div>
                <div class="notice-container left-notice notice-2">
                    <p id="text_keep_eyes_open">
                        @i18n.TranslateText("Instructions.KeepEyesOpen")
                    </p>
                    <img id="image_union_2" alt="union" src="images/union.svg" />
                </div>
                <div class="notice-container left-notice notice-3">
                    <p id="text_keep_head_straight">
                        @i18n.TranslateText("Instructions.KeepHeadStraight")
                    </p>
                    <img id="image_union_3" alt="union" src="images/union.svg" />
                </div>
                <div class="notice-container notice-4">
                    <p id="text_remove_hat">
                        @i18n.TranslateText("Instructions.RemoveHat")
                    </p>
                    <img id="image_union_4" alt="union" src="images/union.svg" />
                </div>
                <div class="notice-container notice-5">
                    <p id="text_remove_glasses">
                        @i18n.TranslateText("Instructions.RemoveGlasses")
                    </p>
                    <img id="image_union_5" alt="union" src="images/union.svg" />
                </div>

                <div class="notice-container notice-6">
                    <p id="text_dont_smile">
                        @i18n.TranslateText("Instructions.DontSmile")
                    </p>
                    <img id="image_union_6" alt="union" src="images/union.svg" />
                </div>
            </div>

            <h2 id="text_conformity" class="conformity">
                @i18n.TranslateText("Instructions.Conformity")
            </h2>

            @if (ShowCloseButton)
            {
                <button id="button_close" class="close-btn" @onclick="Close">
                    @i18n.TranslateText("Instructions.Understood")
                    <img id="image_down_arrow" alt="arrow" src="images/down-white-arrow.svg" />
                </button>
            }
            else
            {
                <img id="image_on_boarding_step_2" class="onboarding-step-image" alt="onboarding-step" src="images/onboarding-step-2.svg" />
            }
        </div>
    </div>
</div>