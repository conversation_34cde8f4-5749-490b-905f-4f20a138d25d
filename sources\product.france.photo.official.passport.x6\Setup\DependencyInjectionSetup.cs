using Kis.Camera.HeightAdjustment.Extensions;
using Kis.Framework.Basic.ProjectInformation;
using Kis.Framework.gRPC.Client;
using Kis.Framework.Ui.Extension;
using Kis.Framework.Ui.LocalizationTool;
using Kis.Framework.Ui.LocalizationTool.LocalizationSources;
using Kis.Framework.Ui.LocalizationTool.LocalizationSources.Excel;
using Kis.Framework.Ui.Services;
using Kis.Library.Camera.Core;
using Kis.Library.Data;
using Kis.Library.HardwareCheck;
using Kis.Library.MakeupRenderer.Extension;
using Kis.Product.France.Photo.Official.Passport.X6.Services;
using Kis.Share.Model.Hardware;

namespace Kis.Product.France.Photo.Official.Passport.X6.Setup
{
    public class OperatorLocalization : Localization { }

    public static class DependencyInjectionSetup
    {
        public static IServiceCollection AddServiceSpecificObjects(this IServiceCollection services)
        {
            services.AddCameraLibrary();
            services.AddClientProvider();
            services.AddBoothClientProvider();
            services.AddCameraHeightAdjustment();
            services.AddCompositionMakeupRenderer();
            services.AddSingleton<KisHardwareCheck>();
            services.AddCommonUi();
            services.AddSingleton<AppState>();

            services.AddSingleton<IKisData, KisData>();

            //Translations
            services.AddSingleton<IFileLocalizationProvider, ExcelLocalizationProvider>(sp =>
            {
                ExcelLocalizationProvider excelLocalizationProvider = new ExcelLocalizationProvider();
                var wwwRootPath = Path.Combine(sp.GetRequiredService<IWebHostEnvironment>().ContentRootPath, "wwwroot");
                excelLocalizationProvider.OpenFile(Path.Combine(wwwRootPath, "texts.xlsx")).Wait();

                return excelLocalizationProvider;
            });

            services.AddSingleton<ILocalization, Localization>(sp =>
            {
                var fileLocationProvider = sp.GetRequiredService<IFileLocalizationProvider>();
                Localization localization = new Localization();
                localization.SetupTranslationsAsync(fileLocationProvider).Wait();

                return localization;
            });

            services.AddSingleton<OperatorLocalization>(services =>
            {
                var pathProvider = services.GetRequiredService<IPathProvider>();
                ExcelLocalizationProvider excelLocalizationProvider = new ExcelLocalizationProvider();
                OperatorLocalization localization = new OperatorLocalization();

                excelLocalizationProvider.OpenFile(pathProvider.GetOperatorFile("texts.xlsx")).Wait();
                localization.SetupTranslationsAsync(excelLocalizationProvider).Wait();

                return localization;
            });

            services.AddSingleton<KisHardwareInformationParser>(services =>
            {
                var pathProvider = services.GetRequiredService<IPathProvider>();
                var hardwareInformationFilePath = pathProvider.GetGlobalConfigurationFile("hardware-information.json");
                var hardwareInformationParser = new KisHardwareInformationParser();
                hardwareInformationParser.Open(hardwareInformationFilePath);

                return hardwareInformationParser;
            });

            return services;
        }
    }
}
