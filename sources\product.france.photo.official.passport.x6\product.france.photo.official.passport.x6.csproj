﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0-windows</TargetFramework>
		<RootNamespace>Kis.Product.France.Photo.Official.Passport.X6</RootNamespace>
		<AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="kis-booth-grpc" />
		<PackageReference Include="kis-camera-height-adjustment" />
		<PackageReference Include="kis-common-basic" />
		<PackageReference Include="kis-common-camera" />
		<PackageReference Include="kis-common-data" />
		<PackageReference Include="kis-common-hardware-check" />
		<PackageReference Include="kis-common-image" />
		<PackageReference Include="kis-common-makeup-renderer" />
		<PackageReference Include="kis-common-share-models" />
		<PackageReference Include="kis-common-sound-manager" />
		<PackageReference Include="kis-common-ui" />
		<PackageReference Include="Microsoft.IO.RecyclableMemoryStream" />
	</ItemGroup>

</Project>
