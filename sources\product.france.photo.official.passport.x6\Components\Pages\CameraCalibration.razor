﻿@page "/calibration"
@using Kis.Framework.Ui.LocalizationTool
@inject ILocalization i18n
@layout EmptyLayout

<div class="container">
    <header>
        <h1 id="text_auto_setup" class="title">
            @i18n.TranslateText("Calibration.Autosetup")
        </h1>
    </header>
    <div class="body">
        <h3 id="text_look_straight_ahead" class="body-title">
            @i18n.TranslateText("Common.LookStraightAhead")
        </h3>
        <img id="image_spinner" alt="logo" class="spinner" src="images/spinner.svg" />
    </div>
    <DefaultFooter />
</div>