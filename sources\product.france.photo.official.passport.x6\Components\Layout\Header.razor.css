﻿.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.title {
    font-weight: normal;
    font-size: 30px;
}

.button-round {
    cursor: pointer;
    background-color: white;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.p-relative {
    position: relative !important;
}
.button-round.cart {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #EDEAE5;
    width: 38px;
    height: 38px;
    border-radius: 50%;
    top: -10px;
    right: -14px;
}

.button-round.back {
    cursor: pointer;
    border: 2px solid #E7E7E7;
    background: white;
}

.button-icon {
    width: 30px;
    height: 30px
}
.button-round.cart > span {
    width: 28px;
    height: 28px;
    font-size: 20px;
    background-color: white;
    border-radius: 50%;
    font-weight: bold;
    text-align: center;
}
.to-pay {
    font-family: Inter, serif;
    font-size: 22px;
    font-style: normal;
    font-weight: 600;
    line-height: 30px;
    width: max-content;
}

.price {
    font-family: Inter, serif;
    font-size: 36px;
    font-style: normal;
    font-weight: 600;
    line-height: 40px;
    text-align: right;
}

.quit-btn {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: black;
    width: 170px;
    height: 70px;
    padding: 20px;
    border-radius: 65px;
    gap: 6px;
    border: 0;
    background-color: white;
    font-size: 20px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: -1px;
    text-align: left;
    color: black;
    letter-spacing: -1px;
    font-family: 'Futura PT', serif;
}