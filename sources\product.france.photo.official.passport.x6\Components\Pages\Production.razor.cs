using Kis.Framework.Basic.ProjectInformation;
using Kis.Framework.Basic.ProjectInformation.Configuration.Localization;
using Kis.Framework.Basic.ProjectInformation.Configuration.Metadata;
using Kis.Framework.Basic.ProjectInformation.Configuration.Operator;
using Kis.Library.Data;
using Kis.Library.Data.Helper;
using Kis.Library.MakeupRenderer;
using Kis.Library.MakeupRenderer.DataModels;
using Kis.Product.France.Photo.Official.Passport.X6.Services;
using Kis.Share.Model.RemoteTransfer.Output;
using Microsoft.AspNetCore.Components;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Net.Mime;
using System.Text;
using Kis.Framework.Ui.LocalizationTool;
using System.Web;
using Kis.Framework.Ui.Services;

namespace Kis.Product.France.Photo.Official.Passport.X6.Components.Pages
{
    public partial class Production
    {
        [Inject] private AppState State { get; set; } = default!;
        [Inject] private IKisData KisData { get; set; } = default!;
        [Inject] private ILocalization Localization { get; set; } = default!;
        [Inject] private IMakeupRenderer MakeupRenderer { get; set; } = default!;
        [Inject] private IPathProvider PathProvider { get; set; } = default!;
        [Inject] private Metadata Metadata { get; set; } = default!;
        [Inject] private OperatorConfiguration Operator { get; set; } = default!;
        [Inject] private LocalizationConfigurationData LocalizationConfiguration { get; set; } = default!;
        [Inject] private NavigationService Navigation { get; set; } = default!;
        [Inject] private ILogger<Production> Logger { get; set; } = default!;

        protected override async void OnAfterRender(bool firstRender)
        {
            if (firstRender)
            {
                await ProcessProduction();
            }
        }

        private async Task ProcessProduction()
        {
            List<string> dataIds = new(6);
            List<string> choosenPhotos = new(2);

            var mediaSavingResult = SavePhotos(dataIds, choosenPhotos);

            (bool result, string makeupLowResDataId, string makeupHighResDataId) = await GenerateMakeup(State.ProductId);

            mediaSavingResult &= result;

            //If all media are saved successfully by KisData
            if (mediaSavingResult)
            {
                ProductOutput output = GenerateOutput(dataIds, choosenPhotos, State.ProductId, makeupLowResDataId, makeupHighResDataId);

                var base64Data = Convert.ToBase64String(
                    Encoding.UTF8.GetBytes(
                    JsonConvert.SerializeObject(output)));
                // Encode base 64 string before put it in the URL
                string urlEncodedData = HttpUtility.UrlEncode(base64Data);
                Navigation.NavigateTo($"{State.SuccessReturnUrl}?json={urlEncodedData}");
            }
            else
            {
                Navigation.NavigateTo(State.ErrorReturnUrl);
            }
        }

        private ProductOutput GenerateOutput(List<string> dataIds, List<string> choosenPhotos, Guid productGuid, string makeupLowResDataId, string makeupHighResDataId)
        {
            KisData.GetProductData(productGuid, makeupLowResDataId);

            return new ProductOutput
            {
                Galleries =
                [
                    new Gallery
                    {
                        Guid = State.GalleryGuid.ToString(),
                        DataIds = dataIds.ToArray()
                    }
                ],
                ChoosenPhotos = choosenPhotos.ToArray(),
                ProductGuid = State.ProductId.ToString(),
                ShoppingCart = new Shoppingcart
                {
                    Texts = [Localization.TranslateText("Cart.ProductName"), string.Empty],
                    ThumbnailId = makeupLowResDataId,
                    AllowMultipleQuantities = true,
                    ProductId = AppState.ProductName
                },
                Outputs =
                                [
                                    new Output()
                                    {
                                        OutputName = "printer-04x06",
                                        Data = [new OutputData()
                                        {
                                            Name = "makeup",
                                            DataId = new DataId()
                                            {
                                                Name = makeupHighResDataId,
                                                Type = "productStorage",
                                                Guid = productGuid.ToString()
                                            }
                                        }]
                                    }
                                ]
            };
        }

        private async Task<(bool, string, string)> GenerateMakeup(Guid productGuid)
        {
            bool dataSavingResult = true;

            if (!RegisterDataForMakeup())
                return (false, string.Empty, string.Empty);

            //know if the chosen photo is icao or not to get the right makeup
            var productPath = PathProvider.GetProductPath(AppState.ProductName);

            var chosenPhotoIsIcao = State.AnalysisResults[State.ChosenPicture].AnalyseResult;
            string makeupPath;
            if (chosenPhotoIsIcao)
            {
                makeupPath = Path.Combine(productPath, "makeup", "icaoCertifiedMakeup.json");
            }
            else
            {
                makeupPath = Path.Combine(productPath, "makeup", "notIcaoCertifiedMakeup.json");
            }

            if (!File.Exists(makeupPath))
            {
                Logger.LogCritical($"No makeup file found to generate the product at path : {makeupPath}");
                Navigation.NavigateTo(State.ErrorReturnUrl);
            }

            CompositionMakeup makeup = JsonConvert.DeserializeObject<CompositionMakeup>(File.ReadAllText(makeupPath))!;

            //generate high resolution
            var highResolutionComposition = await MakeupRenderer.BuildCompositionAsync(productGuid, productPath, Localization, "fr-FR", makeup, CompositionModes.FullCompositionHighResolution);
            var makeupHighResDataId = DataIdHelper.ToDataId("Thumbnail", DataIdType.MakeUp, DataIdPhotoType.None, DataIdPhotoQuality.HighRes, 1);
            dataSavingResult &= KisData.SaveProductData(State.ProductId, makeupHighResDataId, MediaTypeNames.Image.Bmp, highResolutionComposition.GeneratedCompositionImage.ToByteArray(Framework.Basic.Image.EImageFormat.Bmp));

            //resizing high resolution /4 to get the low resolution
            var lowResolutionMakeup = highResolutionComposition.GeneratedCompositionImage;
            lowResolutionMakeup.Resize(highResolutionComposition.GeneratedCompositionImage.Width / 4, highResolutionComposition.GeneratedCompositionImage.Height / 4);
            var makeupLowResDataId = DataIdHelper.ToDataId("Thumbnail", DataIdType.MakeUp, DataIdPhotoType.None, DataIdPhotoQuality.LowRes, 1);
            dataSavingResult &= KisData.SaveProductData(State.ProductId, makeupLowResDataId, MediaTypeNames.Image.Bmp, lowResolutionMakeup.ToByteArray(Framework.Basic.Image.EImageFormat.Bmp));

            return (dataSavingResult, makeupLowResDataId, makeupHighResDataId);
        }

        private bool RegisterDataForMakeup()
        {
            bool dataSavingResult = true;

            //Get the product price and comupte the make-up requested variables
            string priceFilePath = Path.Combine(PathProvider.GetWorkflowConfigurationFolder("menu"), AppState.ProductName, "price.json");
            var priceData = (JObject)JsonConvert.DeserializeObject(File.ReadAllText(priceFilePath))!;
            decimal netCost = (decimal)priceData["price"]!;
            var tvaPercent = (decimal)Operator.Vat;
            var currencyCulture = LocalizationConfiguration.OperatorCulture.Currency;
            var localizedCost = CurrencyLocalization.LocalizeAmount(netCost, currencyCulture);
            var vatAmount = netCost - netCost / (1 + tvaPercent / 100);
            var localizedVatAmount = CurrencyLocalization.LocalizeAmount(vatAmount, currencyCulture);

            //DATA8 (utf8) ProductCost VatAmount
            var dataId = DataIdHelper.ToDataId("ProductCost", DataIdType.Text, DataIdPhotoType.None, DataIdPhotoQuality.None, 1);
            dataSavingResult &= KisData.SaveProductData(State.ProductId, dataId, MediaTypeNames.Text.RichText, Encoding.UTF8.GetBytes(localizedCost));

            dataId = DataIdHelper.ToDataId("VatAmount", DataIdType.Text, DataIdPhotoType.None, DataIdPhotoQuality.None, 1);

            dataSavingResult &= KisData.SaveProductData(State.ProductId, dataId, MediaTypeNames.Text.RichText, Encoding.UTF8.GetBytes(localizedVatAmount));

            //DATA (ascii) VatRate DateTime MachineGuid BranchPhone
            dataId = DataIdHelper.ToDataId("VatRate", DataIdType.Text, DataIdPhotoType.None, DataIdPhotoQuality.None, 1);
            dataSavingResult &= KisData.SaveProductData(State.ProductId, dataId, MediaTypeNames.Text.RichText, Encoding.ASCII.GetBytes(tvaPercent.ToString("0.00")));

            dataId = DataIdHelper.ToDataId("DateTime", DataIdType.Text, DataIdPhotoType.None, DataIdPhotoQuality.None, 1);
            dataSavingResult &= KisData.SaveProductData(State.ProductId, dataId, MediaTypeNames.Text.RichText, Encoding.ASCII.GetBytes(DateTime.Now.ToString(@"dd/MM/yyyy HH\Hmm")));

            dataId = DataIdHelper.ToDataId("MachineGuid", DataIdType.Text, DataIdPhotoType.None, DataIdPhotoQuality.None, 1);
            dataSavingResult &= KisData.SaveProductData(State.ProductId, dataId, MediaTypeNames.Text.RichText, Encoding.ASCII.GetBytes(Metadata.MachineSerial));

            dataId = DataIdHelper.ToDataId("BranchPhone", DataIdType.Text, DataIdPhotoType.None, DataIdPhotoQuality.None, 1);
            dataSavingResult &= KisData.SaveProductData(State.ProductId, dataId, MediaTypeNames.Text.RichText, Encoding.ASCII.GetBytes(Operator.Hotline));

            return dataSavingResult;
        }

        private bool SavePhotos(List<string> dataIds, List<string> choosenPhotos)
        {
            bool dataSavingResult = true;

            //save the captures with KisData
            for (int i = 0; i < 3; i++)
            {
                if (State.RawImages.Count > i)
                {
                    var dataId = DataIdHelper.ToDataId("Capture", DataIdType.Photo, DataIdPhotoType.Raw,
                        DataIdPhotoQuality.HighRes, i + 1);
                    dataIds.Add(dataId);
                    dataSavingResult &= KisData.AddMedia(State.GalleryGuid, dataId, MediaTypeNames.Image.Bmp, State.RawImages[i]);

                    if (i == State.ChosenPicture)
                    {
                        choosenPhotos.Add(dataId);
                        dataId = DataIdHelper.ToDataId("Capture", DataIdType.Photo, DataIdPhotoType.Raw,
                        DataIdPhotoQuality.HighRes, 1);
                        dataSavingResult &= KisData.SaveProductData(State.ProductId, dataId, MediaTypeNames.Image.Bmp, State.RawImages[i]);
                    }
                }

                if (!string.IsNullOrEmpty(State.Base64IcaoImages[i]))
                {
                    var dataId = DataIdHelper.ToDataId("Capture", DataIdType.Photo, DataIdPhotoType.Icao,
                        DataIdPhotoQuality.HighRes, i + 1);
                    dataIds.Add(dataId);
                    dataSavingResult &= KisData.AddMedia(State.GalleryGuid, dataId, MediaTypeNames.Image.Bmp, Convert.FromBase64String(State.Base64IcaoImages[i]));

                    if (i == State.ChosenPicture)
                    {
                        choosenPhotos.Add(dataId);
                        dataId = DataIdHelper.ToDataId("Capture", DataIdType.Photo, DataIdPhotoType.Icao,
                        DataIdPhotoQuality.HighRes, 1);
                        dataSavingResult &= KisData.SaveProductData(State.ProductId, dataId, MediaTypeNames.Image.Bmp, Convert.FromBase64String(State.Base64IcaoImages[i]));
                    }
                }
            }

            return dataSavingResult;
        }
    }
}
