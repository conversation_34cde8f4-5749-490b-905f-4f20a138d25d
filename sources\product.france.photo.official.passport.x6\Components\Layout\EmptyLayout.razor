﻿@using Kis.SoundManager
@using Kis.Framework.Ui.LocalizationTool
@inherits LayoutComponentBase
@inject ILocalization i18n

<SoundPlayer PlaylistFilename="SoundPlaylists.json" CurrentLanguage="@i18n.CurrentLanguage.Name" @ref="Player" />

<CascadingValue Value="Player">
    @Body
</CascadingValue>

@code
{
    private SoundPlayer? _player;

    private SoundPlayer? Player
    {
        get => _player;
        set
        {
            _player = value;
            if (value is not null)
                StateHasChanged();
        }
    }
}
