using Kis.Framework.Ui.LocalizationTool;
using Kis.Product.France.Photo.Official.Passport.X6.Services;
using Kis.SoundManager;
using Microsoft.AspNetCore.Components;

namespace Kis.Product.France.Photo.Official.Passport.X6.Components.Pages;

public partial class Preview
{
	[Inject] private AppState State { get; set; } = default!;
	[Inject] private ILocalization Localization { get; set; } = default!;

	[CascadingParameter] public SoundPlayer Player { get; set; } = default!;

	private bool CanRetry => State.CurrentPicture >= 3;
    private bool _showRetryConfirm = false;
	private readonly SemaphoreSlim _navigationLock = new(1); // Lock to prevent multiple concurrent navigations

	protected override void OnInitialized()
	{
		ChoosePicture(State.CurrentPicture - 1);
	}

	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		if (firstRender)
		{
			await Player.Play("PhotoReviewIntro");
		}
	}

	private async void GoToLive()
    {
		await _navigationLock.WaitAsync(); // Lock without releasing
        Navigation.NavigateTo("/live");
    }

	private void ChoosePicture(int picture)
	{
		State.ChosenPicture = picture;
		StateHasChanged();
	}

	private async void ValidatePhoto()
	{
		await _navigationLock.WaitAsync(); // Lock without releasing
		Navigation.NavigateTo("/production");
	}

	private void ConfirmToRetry()
    {
        State.Reset();
        _showRetryConfirm = false;
        StateHasChanged();
        Navigation.NavigateTo("/live");
    }

    private void CancelRetry()
    {
        _showRetryConfirm = false;
        StateHasChanged();
    }

    private async void Retry()
    {
		await _navigationLock.WaitAsync();
        _showRetryConfirm = true;
        StateHasChanged();
		_navigationLock.Release();
    }
}