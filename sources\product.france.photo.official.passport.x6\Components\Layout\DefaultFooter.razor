@using Kis.Framework.Basic.ProjectInformation.Configuration.Operator
@using Kis.Framework.Ui.LocalizationTool
@using Kis.Product.France.Photo.Official.Passport.X6.Setup
@inject ILocalization i18n
@inject OperatorLocalization OperatorLocalization
@inject OperatorConfiguration Operator
@inject OperatorConfiguration OperatorConfiguration

<div class="footer">
    <img id="image_black_logo" alt="logo" class="footer-img" src="operator/images/black.png" />
    <p id="text_call_center" class="footer-paragraph">
        <span id="text_need_help" class="footer-span">
            @i18n.TranslateText("Footer.Text.NeedHelp")
        </span>&nbsp;
        @(string.Format(OperatorLocalization.TranslateText("Footer.Text.CallCenter", @i18n.CurrentLanguage), OperatorConfiguration.Hotline))
    </p>
</div>