﻿using Kis.Camera.HeightAdjustment;
using Kis.Framework.gRPC.Client;
using Kis.Framework.gRPC.Protos;
using Kis.Framework.Ui.Services;
using Kis.Product.France.Photo.Official.Passport.X6.Services;
using Kis.SoundManager;
using Microsoft.AspNetCore.Components;

namespace Kis.Product.France.Photo.Official.Passport.X6.Components.Pages;

public partial class CameraCalibration
{
    [Inject] private NavigationService Navigation { get; set; } = default!;
    [Inject] private AppState State { get; set; } = default!;
    [Inject] private ICameraHeightAdjustment CameraHeightAdjustment { get; set; } = default!;
    [Inject] private IServiceIoBoardClient ServiceIoBoardClient { get; set; } = default!;

    [CascadingParameter] public SoundPlayer Player { get; set; } = default!;

    private CancellationTokenSource _cameraAdjustmentTokenSource = new();
    private TaskCompletionSource _tcsPlaySound = new();

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                if (State.MachineType ==  MachineType.FIXED_STOOL)
                {
                    var moveCameraTask = Task.Run(async () =>
                    {
                        var result = await ServiceIoBoardClient.MoveCameraToTop(Framework.gRPC.Client.Model.EMotorSpeed.Medium);
                        if (result != EMotorCommandResult.Ok && result != EMotorCommandResult.CommandStopped && result != EMotorCommandResult.SensorError)
                        {
                            Navigation.NavigateToError(@"Error while MoveCamera(up = {up})", "ioboard");
                            return;
                        }
                    });
                    await Player.Play("AutoEyeDetection", () => {
                        _tcsPlaySound.SetResult();
                        return ValueTask.CompletedTask;
                    });

                    // Move camera and play sound in parallel
                    await Task.WhenAll(moveCameraTask, _tcsPlaySound.Task);

                    _cameraAdjustmentTokenSource.CancelAfter(TimeSpan.FromSeconds(15));
                    await CameraHeightAdjustment.AutoSet(cancellationToken: _cameraAdjustmentTokenSource.Token);
                }

                Navigation.NavigateTo("/live");
            }
            catch (Exception e)
            {
                Navigation.NavigateToError($"Cannot auto adjust camera: {e}", "camera");
            }
        }
    }
}