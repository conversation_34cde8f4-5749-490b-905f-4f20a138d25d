using Kis.Camera.Core.Extensions;
using Kis.Framework.gRPC.Client;
using Kis.Framework.gRPC.Client.Model;
using Kis.Framework.Ui.Services;
using Kis.Library.Camera.Common;
using Kis.Library.Camera.Core;
using Kis.Product.France.Photo.Official.Passport.X6.Services;
using Kis.SoundManager;
using Microsoft.AspNetCore.Components;
using System.Drawing.Imaging;

namespace Kis.Product.France.Photo.Official.Passport.X6.Components.Pages;

public partial class Countdown
{
	[Inject] private NavigationService Navigation { get; set; } = default!;
	[Inject] private ICameraBusinessCore Camera { get; set; } = default!;
	[Inject] private IServiceIcaoCheckerClient ServiceIcaoCheckerClient { get; set; } = default!;
	[Inject] private IServiceIoBoardClient ServiceIoBoardClient { get; set; } = default!;
	[Inject] private AppState State { get; set; } = default!;
	[Inject] private ILogger<Countdown> Logger { get; set; } = default!;

	[CascadingParameter] public SoundPlayer Player { get; set; } = default!;

	private bool BlackScreenIsHidden { get; set; } = true;
	private bool WaitingSpinnerIsHidden { get; set; } = true;
	private bool CountdownIsHidden { get; set; } = true;

	private int _countdown = 3;
	private TaskCompletionSource _tcs = new();

    protected async override Task OnInitializedAsync()
    {
        if (!await Camera.PrepareCamera(ResolutionMode.ShootGrayBackground))
        {
            Logger.LogError($"OnInitializedAsync - PrepareCamera to {ResolutionMode.ShootGrayBackground} is fail");
            Navigation.NavigateToError($"Error while switch camera mode", "camera");
            return;
        }

        //play intro sound
        await Player.Play("TakePhotoIntro", async () =>
        {
            _tcs.SetResult();
        });

        await _tcs.Task;

        //Start countdown
        CountdownIsHidden = false;
        await InvokeAsync(StateHasChanged);

        try
        {
            //Blink led
            CancellationTokenSource ledToken = new();
            var taskLed = Camera.BlinkLed(500, ledToken.Token);
            for (int i = 3; i >= 1; i--)
            {
                _tcs = new();
                _countdown = i;
                await InvokeAsync(StateHasChanged);
                await Player.Play($"TakePhoto{i}", async () =>
                {
                    _tcs.SetResult();
                });

                await _tcs.Task;
            }

            await ledToken.CancelAsync();
            await taskLed.WaitAsync(TimeSpan.FromMilliseconds(550));
        }
        catch (Exception ex)
        {
            Logger.LogError($"OnInitializedAsync - Exception: {ex}");
            Navigation.NavigateToError($"Error while blink led power state: {ex}", "camera");
            return;
        }

        CancellationTokenSource cts = new();
        cts.CancelAfter(TimeSpan.FromSeconds(20));
        bool isProcessedPhoto = await ProcessPhotoShootAsync(cts.Token);

        if (cts.IsCancellationRequested)
        {
            Navigation.NavigateToError("Timeout while processing photo shoot", "camera");
            return;
        }
        if (isProcessedPhoto)
        {
            Navigation.NavigateTo("/preview");
        }       
    }

    private async Task<bool> ProcessPhotoShootAsync(CancellationToken token = default)
	{
        bool result = true;
        try
        {
            if (!await ServiceIoBoardClient.SetLightMode(ELightMode.ShootGrayBackground.ToString()))
            {
                Navigation.NavigateToError("Cannot set ShootGrayBackground light mode", "ioboard");
                result = false;
            }

            //display black screen
            BlackScreenIsHidden = false;
            await InvokeAsync(StateHasChanged);

            await TakePicture();

            var analyseTask = AnalysePicture(token);

            //hide black screen and display waiting spinner
            BlackScreenIsHidden = true;
            WaitingSpinnerIsHidden = false;
            await InvokeAsync(StateHasChanged);

            if (!await ServiceIoBoardClient.SetLightMode(ELightMode.Customer.ToString()))
            {
                Navigation.NavigateToError("Cannot set Customer light mode", "ioboard");
                result = false;
            }

            await analyseTask.WaitAsync(token);
        }
        catch (Exception ex)
        {
            Navigation.NavigateToError($"Error while taking the photo: {ex}", "camera");
            result = false;
        }
        return result;
	}

	private async Task TakePicture()
	{
		try
		{
            await Task.Delay(1000);
            
			// Take photo and play click sound
			if (!await Camera.ExecuteCyclePhoto())
			{
				Logger.LogError("Error while taking the picture");
                Navigation.NavigateToError("Error while taking the picture", "camera");
				return;
			}

			_ = Player.Play("TakePhotoClick");
		}
		catch (Exception e)
		{
			Navigation.NavigateToError($"Error while taking the picture: {e}", "camera");
		}
	}

	private async Task AnalysePicture(CancellationToken token = default)
	{
		try
		{
			using var bmp = await Camera.RetrievePhotoTaken();
			Camera.Dispose();
			if (bmp is not null)
			{
				var bytes = bmp.ToByte(ImageFormat.Bmp);

				State.RawImages.Add(bytes);
				var analysis = await ServiceIcaoCheckerClient.AnalyseAndCropIcao(
					bytes,
					new OfficialIdParameters
					{
						Country = "france",
						Document = "passport"
					},
					token);
				if (analysis.icaoAnalysis is not null)
				{
					State.Base64IcaoImages[State.CurrentPicture] = Convert.ToBase64String(analysis.croppedPhoto);
					State.AnalysisResults[State.CurrentPicture] = analysis.icaoAnalysis;
					State.CurrentPicture++;
					return;
				}
			}
		}
		catch (Exception e)
		{
			Logger.LogError(e, "Error while retrieving the picture or AnalyseAndCropIcao");
		}

		Navigation.NavigateToError("Cannot analyse Icao and crop", "icao");
	}
}